# 分析当前11类分类的详细分布
library(dplyr)

# 加载数据
load("dta1_20240903.RData")

# 当前的11类分类
dta1$industry_type11 <- case_when(
  # Energy
  dta1$IndustryName %in% c("石油和天然气开采业",
                           "石油加工、炼焦及核燃料加工业",
                           "煤炭开采和洗选业") ~ "Energy",
  # Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                           "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                           "木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业",
                           "金属制品业") ~ "Materials",
  # Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业",
                           "电气机械及器材制造业","通用设备制造业",
                           "装卸搬运和运输代理业","道路运输业","水上运输业",
                           "航空运输业","铁路运输业","仓储业","其他制造业",
                           "专业技术服务业","其他服务业",
                           "废弃资源综合利用业", "综合", "金属制品、机械和设备修理业",
                           "邮政业","印刷和记录媒介复制业",
                           "房屋建筑业","建筑安装业","土木工程建筑业",
                           "建筑装饰和其他建筑业","生态保护和环境治理业",
                           "公共设施管理业") ~ "Industrials",
  # Consumer Discretionary
  dta1$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业",
                           "教育","纺织业","零售业","纺织服装、服饰业",
                           "文教、工美、体育和娱乐用品制造业","文化艺术业",
                           "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业",
                           "机动车、电子产品和日用产品修理业",
                           "汽车制造业") ~ "Consumer Discretionary",
  # Consumer Staples
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业",
                           "农业","畜牧业","渔业",
                           "农、林、牧、渔服务业") ~ "Consumer Staples",
  # Health Care
  dta1$IndustryName %in% c("医药制造业","卫生") ~ "Health Care",
  # Financials
  dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业",
                           "其他金融业","租赁业","商务服务业") ~ "Financials",
  # Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业",
                           "计算机、通信和其他电子设备制造业",
                           "仪器仪表制造业",
                           "研究和试验发展","科技推广和应用服务业") ~ "Information Technology",
  # Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务",
                           "广播、电视、电影和影视录音制作业",
                           "新闻和出版业","互联网和相关服务") ~ "Communication Services",
  # Utilities
  dta1$IndustryName %in% c("电力、热力生产和供应业",
                           "燃气生产和供应业",
                           "水的生产和供应业") ~ "Utilities",
  # Real Estate
  dta1$IndustryName %in% c("房地产业","开发辅助活动") ~ "Real Estate",
  TRUE ~ NA_character_
)

# 分析每个类别的详细构成
cat("=== 当前11类分类详细分析 ===\n\n")

for(category in sort(unique(dta1$industry_type11[!is.na(dta1$industry_type11)]))) {
  cat("【", category, "】\n")
  
  # 获取该类别的所有行业
  industries_in_category <- dta1 %>%
    filter(industry_type11 == category) %>%
    group_by(IndustryName) %>%
    summarise(
      count = n(),
      companies = length(unique(Symbol)),
      .groups = 'drop'
    ) %>%
    arrange(desc(count))
  
  cat("观测数:", sum(industries_in_category$count), 
      "| 公司数:", sum(industries_in_category$companies), "\n")
  
  print(industries_in_category)
  cat("\n")
}

# 识别可能重新分配的行业
cat("=== 建议重新分配的行业 ===\n\n")

cat("1. 边界模糊的行业（可以合理重新分类）：\n")
ambiguous_industries <- c(
  "综合",                           # 目前在Industrials
  "其他制造业",                     # 目前在Industrials  
  "其他服务业",                     # 目前在Industrials
  "商务服务业",                     # 目前在Financials
  "租赁业",                         # 目前在Financials
  "开采辅助活动",                   # 目前在Materials
  "生态保护和环境治理业",           # 目前在Industrials
  "公共设施管理业",                 # 目前在Industrials
  "仪器仪表制造业",                 # 目前在Information Technology
  "研究和试验发展",                 # 目前在Information Technology
  "科技推广和应用服务业",           # 目前在Information Technology
  "专业技术服务业",                 # 目前在Industrials
  "开发辅助活动"                    # 目前在Real Estate
)

for(industry in ambiguous_industries) {
  if(industry %in% dta1$IndustryName) {
    current_category <- dta1$industry_type11[dta1$IndustryName == industry & !is.na(dta1$industry_type11)][1]
    count <- sum(dta1$IndustryName == industry, na.rm = TRUE)
    companies <- length(unique(dta1$Symbol[dta1$IndustryName == industry]))
    
    cat("- ", industry, " (目前:", current_category, ") - ", count, "观测,", companies, "公司\n")
  }
}

cat("\n=== 各类别当前规模 ===\n")
category_summary <- dta1 %>%
  filter(!is.na(industry_type11)) %>%
  group_by(industry_type11) %>%
  summarise(
    observations = n(),
    companies = length(unique(Symbol)),
    .groups = 'drop'
  ) %>%
  arrange(desc(observations))

print(category_summary)
