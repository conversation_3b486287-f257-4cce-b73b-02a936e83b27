# 11类行业分类回归分析脚本

# 加载必要的库
library(plm)
library(lme4)
library(lmerTest)
library(stargazer)
library(sjPlot)
library(performance)
library(dplyr)

# 加载数据
load("dta1_20240903.RData")

cat("=== 11类行业分类分析 ===\n")
cat("原始数据概览:\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")

# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # Energy
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "石油加工、炼焦及核燃料加工业", "石油和天然气开采业", 
                           "煤炭开采和洗选业") ~ "Energy",
  
  # Consumer Discretionary
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业", "住宿业", 
                           "餐饮业", "体育", 
                           "纺织服装、服饰业", "文教、工美、体育和娱乐用品制造业", 
                           "文化艺术业", "皮革、毛皮、羽毛及其制品和制鞋业", 
                           "家具制造业", "机动车、电子产品和日用产品修理业") ~ "Consumer Discretionary",
  
  # Real Estate
  dta1$IndustryName %in% c("房地产业", "房屋建筑业", 
                           "建筑安装业", "土木工程建筑业", 
                           "建筑装饰和其他建筑业") ~ "Real Estate",
  
  # Utilities
  dta1$IndustryName %in% c("水的生产和供应业", "公共设施管理业", 
                           "生态保护和环境治理业") ~ "Utilities",
  
  # Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "互联网和相关服务", 
                           "邮政业") ~ "Communication Services",
  
  # Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "计算机、通信和其他电子设备制造业", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业", "专用设备制造业", 
                           "通用设备制造业", "仪器仪表制造业", 
                           "装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "航空运输业", 
                           "铁路运输业", "仓储业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "金属制品业", "其他制造业", 
                           "橡胶和塑料制品业", "木材加工及木、竹、藤、棕、草制品业", 
                           "废弃资源综合利用业", "金属制品、机械和设备修理业") ~ "Industrials",
  
  # Consumer Staples
  dta1$IndustryName %in% c("食品制造业", "农副食品加工业", 
                           "畜牧业", "渔业", 
                           "农业", "林业", 
                           "农、林、牧、渔服务业") ~ "Consumer Staples",
  
  # Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业", "化学纤维制造业", 
                           "非金属矿物制品业", "造纸及纸制品业", 
                           "印刷和记录媒介复制业") ~ "Materials",
  
  # Health Care
  dta1$IndustryName %in% c("医药制造业", "卫生") ~ "Health Care",
  
  # Financials
  dta1$IndustryName %in% c("货币金融服务", "商务服务业", 
                           "资本市场服务", "其他金融业", 
                           "保险业", "租赁业") ~ "Financials"
)

# 创建去除NA的数据集
dta1_11class <- dta1 %>% filter(!is.na(industry_type11))

cat("\n11类分类结果:\n")
cat("有效观测数:", nrow(dta1_11class), "\n")
cat("NA观测数:", sum(is.na(dta1$industry_type11)), "\n")
cat("覆盖率:", round(nrow(dta1_11class)/nrow(dta1)*100, 1), "%\n")

cat("\n=== 运行PLM固定效应模型 ===\n")

# P3 PLM模型
p3way3_11 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                 ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                 data=dta1_11class, index=c("EndYear"), model="within")

# P4 PLM模型
p4m2_11 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1_11class, index=c("EndYear"), model="within")

p4m4_11 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1_11class, index=c("EndYear"), model="within")

cat("PLM模型完成\n")

cat("\n=== 运行LMER混合效应模型 ===\n")

# 基础混合效应模型
p3mix1_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1_11class)

# 包含行业随机效应
p3mix4_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE) + (1 | industry_type11), 
                  data = dta1_11class)

# 中央政治关联模型
p4mix2_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                  (1 | PROVINCE) + (1 | industry_type11),
                  data = dta1_11class)

# 随机斜率模型 - 行业层面的交互效应随机斜率（简化版本）
p3mix_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE) + (0 + connection_num | industry_type11),
                       data = dta1_11class)

p4mix_central_slope_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               (1 | PROVINCE) + (0 + central_connection | industry_type11),
                               data = dta1_11class)

p4mix_local_slope_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             (1 | PROVINCE) + (0 + local_connection | industry_type11),
                             data = dta1_11class)

# 多层级随机效应模型（包含行业）
p3mix_multi_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage +
                                RegisterCapital_log + (1 | PROVINCE) + (1 | CITY) + (1 | industry_type11),
                                data = dta1_11class)

p3mix_nested_industry_11 <- lmer(Environmental_Information_Disclosure ~ connection_num + ROA + ESG_Rate + Leverage +
                                 RegisterCapital_log + (1 | PROVINCE/CITY) + (1 | industry_type11),
                                 data = dta1_11class)

# 包含行业固定效应的混合效应模型
p3mix_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE),
                                data = dta1_11class)

p4mix_fixed_central_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               as.factor(industry_type11) + (1 | PROVINCE),
                               data = dta1_11class)

cat("LMER模型完成\n")

cat("\n=== 输出PLM模型结果 ===\n")
stargazer(p3way3_11, p4m2_11, p4m4_11,
          type="text", 
          column.labels = c("P3 总关联", "P4 中央关联", "P4 地方关联"),
          title="11类行业分类PLM回归结果",
          omit="as.factor",
          notes=c("行业固定效应 (11类) 和省份固定效应已包含"),
          dep.var.labels = "环境信息披露")

# 模型比较
cat("\n=== 模型性能比较 ===\n")
plm_models <- list(p3way3_11, p4m2_11, p4m4_11)
lmer_models <- list(p3mix1_11, p3mix4_11, p4mix2_11)

comparison_data <- data.frame(
  Model = c("P3 总关联", "P4 中央关联", "P4 地方关联"),
  PLM_R2 = sapply(plm_models, function(x) round(summary(x)$r.squared[1], 4)),
  PLM_Obs = sapply(plm_models, nobs),
  LMER_R2_marginal = sapply(lmer_models, function(x) round(performance::r2(x)$R2_marginal, 4)),
  LMER_R2_conditional = sapply(lmer_models, function(x) round(performance::r2(x)$R2_conditional, 4)),
  LMER_Obs = sapply(lmer_models, nobs)
)

print(comparison_data)

cat("\n=== ICC分析 ===\n")
icc_basic <- performance::icc(p3mix1_11)
icc_industry <- performance::icc(p3mix4_11)
icc_slope <- performance::icc(p3mix_slope_11)

cat("基础模型 (省份随机效应) ICC:", round(icc_basic$ICC_adjusted, 4), "\n")
cat("行业模型 (省份+行业随机效应) ICC:", round(icc_industry$ICC_adjusted, 4), "\n")
cat("随机斜率模型 ICC:", round(icc_slope$ICC_adjusted, 4), "\n")

cat("\n=== 随机斜率模型性能 ===\n")
slope_models <- list(p3mix_slope_11, p4mix_central_slope_11, p4mix_local_slope_11)
slope_r2_marginal <- sapply(slope_models, function(x) round(performance::r2(x)$R2_marginal, 4))
slope_r2_conditional <- sapply(slope_models, function(x) round(performance::r2(x)$R2_conditional, 4))

cat("P3 随机斜率模型 R²(边际):", slope_r2_marginal[1], "\n")
cat("P3 随机斜率模型 R²(条件):", slope_r2_conditional[1], "\n")
cat("P4 中央随机斜率模型 R²(边际):", slope_r2_marginal[2], "\n")
cat("P4 中央随机斜率模型 R²(条件):", slope_r2_conditional[2], "\n")
cat("P4 地方随机斜率模型 R²(边际):", slope_r2_marginal[3], "\n")
cat("P4 地方随机斜率模型 R²(条件):", slope_r2_conditional[3], "\n")

# 新增模型性能
cat("\n=== 新增模型性能 ===\n")
multi_models <- list(p3mix_multi_industry_11, p3mix_nested_industry_11)
fixed_models <- list(p3mix_fixed_industry_11, p4mix_fixed_central_11)

multi_r2_marginal <- sapply(multi_models, function(x) round(performance::r2(x)$R2_marginal, 4))
multi_r2_conditional <- sapply(multi_models, function(x) round(performance::r2(x)$R2_conditional, 4))
fixed_r2_marginal <- sapply(fixed_models, function(x) round(performance::r2(x)$R2_marginal, 4))
fixed_r2_conditional <- sapply(fixed_models, function(x) round(performance::r2(x)$R2_conditional, 4))

cat("多层级+行业模型 R²(边际):", multi_r2_marginal[1], "\n")
cat("多层级+行业模型 R²(条件):", multi_r2_conditional[1], "\n")
cat("嵌套+行业模型 R²(边际):", multi_r2_marginal[2], "\n")
cat("嵌套+行业模型 R²(条件):", multi_r2_conditional[2], "\n")
cat("行业固定效应模型 R²(边际):", fixed_r2_marginal[1], "\n")
cat("行业固定效应模型 R²(条件):", fixed_r2_conditional[1], "\n")
cat("中央固定效应模型 R²(边际):", fixed_r2_marginal[2], "\n")
cat("中央固定效应模型 R²(条件):", fixed_r2_conditional[2], "\n")

# 保存结果
stargazer(p3way3_11, p4m2_11, p4m4_11,
          type="html", 
          out="industry_11class_results.html",
          column.labels = c("P3 总关联", "P4 中央关联", "P4 地方关联"),
          title="11类行业分类回归结果",
          omit="as.factor",
          notes=c("*** p<0.01, ** p<0.05, * p<0.1", 
                  "省份和行业固定效应已包含"),
          dep.var.labels = "环境信息披露")

cat("\n结果已保存到 industry_11class_results.html\n")
