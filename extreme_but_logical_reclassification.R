# 极端但仍有逻辑的重新分类方案
library(plm)
library(lme4)
library(lmerTest)
library(dplyr)

# 加载数据
load("dta1_20240903.RData")

# 方案1：按产业链重新分类（可能降低区分度）
create_value_chain_classification <- function(data) {
  data$industry_value_chain <- case_when(
    # 上游：原材料和能源
    data$IndustryName %in% c("石油和天然气开采业","煤炭开采和洗选业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","开采辅助活动","林业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Upstream",
    
    # 中游制造1：基础制造
    data$IndustryName %in% c("石油加工、炼焦及核燃料加工业","化学原料及化学制品制造业","化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业","金属制品业") ~ "Manufacturing_Basic",
    
    # 中游制造2：设备制造
    data$IndustryName %in% c("专用设备制造业","通用设备制造业","电气机械及器材制造业","铁路、船舶、航空航天和其它运输设备制造业","汽车制造业","仪器仪表制造业","计算机、通信和其他电子设备制造业","其他制造业") ~ "Manufacturing_Equipment",
    
    # 中游制造3：消费品制造
    data$IndustryName %in% c("农副食品加工业","食品制造业","酒、饮料和精制茶制造业","纺织业","纺织服装、服饰业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","文教、工美、体育和娱乐用品制造业","医药制造业") ~ "Manufacturing_Consumer",
    
    # 基础设施：建筑和公用事业
    data$IndustryName %in% c("房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业","电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业","生态保护和环境治理业","公共设施管理业","废弃资源综合利用业") ~ "Infrastructure",
    
    # 物流运输
    data$IndustryName %in% c("装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","邮政业") ~ "Logistics",
    
    # 商业服务
    data$IndustryName %in% c("批发业","零售业","住宿业","餐饮业","租赁业","商务服务业","专业技术服务业","其他服务业","机动车、电子产品和日用产品修理业","金属制品、机械和设备修理业") ~ "Business_Services",
    
    # 金融服务
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业") ~ "Financial_Services",
    
    # 信息服务
    data$IndustryName %in% c("软件和信息技术服务业","电信、广播电视和卫星传输服务","互联网和相关服务","研究和试验发展","科技推广和应用服务业") ~ "Information_Services",
    
    # 文化娱乐
    data$IndustryName %in% c("广播、电视、电影和影视录音制作业","新闻和出版业","印刷和记录媒介复制业","文化艺术业","体育","教育","居民服务业") ~ "Culture_Entertainment",
    
    # 其他（包括房地产、卫生、综合等）
    data$IndustryName %in% c("房地产业","开发辅助活动","卫生","综合") ~ "Others",
    
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案2：按所有制和规模重新分类（可能创造内部异质性）
create_ownership_size_classification <- function(data) {
  # 这个方案故意将不同规模和性质的企业混合在一起
  data$industry_mixed_logic <- case_when(
    # 大型国有主导行业
    data$IndustryName %in% c("石油和天然气开采业","煤炭开采和洗选业","电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业","铁路运输业","电信、广播电视和卫星传输服务","货币金融服务","资本市场服务","保险业") ~ "Large_SOE_Dominated",
    
    # 重工业制造
    data$IndustryName %in% c("石油加工、炼焦及核燃料加工业","化学原料及化学制品制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","专用设备制造业","通用设备制造业","电气机械及器材制造业","铁路、船舶、航空航天和其它运输设备制造业") ~ "Heavy_Manufacturing",
    
    # 轻工业制造（混合传统和现代）
    data$IndustryName %in% c("纺织业","纺织服装、服饰业","家具制造业","文教、工美、体育和娱乐用品制造业","皮革、毛皮、羽毛及其制品和制鞋业","计算机、通信和其他电子设备制造业","汽车制造业","医药制造业") ~ "Light_Manufacturing",
    
    # 资源开采和加工
    data$IndustryName %in% c("黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","开采辅助活动","非金属矿物制品业","橡胶和塑料制品业","化学纤维制造业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业","金属制品业") ~ "Resource_Processing",
    
    # 农业食品
    data$IndustryName %in% c("农业","畜牧业","渔业","林业","农、林、牧、渔服务业","农副食品加工业","食品制造业","酒、饮料和精制茶制造业") ~ "Agriculture_Food",
    
    # 建筑房地产
    data$IndustryName %in% c("房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业","房地产业","开发辅助活动") ~ "Construction_RealEstate",
    
    # 运输物流
    data$IndustryName %in% c("装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","仓储业","邮政业") ~ "Transportation_Logistics",
    
    # 商业贸易（混合批发零售和服务）
    data$IndustryName %in% c("批发业","零售业","住宿业","餐饮业","商务服务业","租赁业","其他服务业","居民服务业") ~ "Commerce_Trade",
    
    # 科技信息（混合软硬件）
    data$IndustryName %in% c("软件和信息技术服务业","仪器仪表制造业","研究和试验发展","科技推广和应用服务业","互联网和相关服务") ~ "Technology_Information",
    
    # 文化教育卫生
    data$IndustryName %in% c("广播、电视、电影和影视录音制作业","新闻和出版业","印刷和记录媒介复制业","文化艺术业","体育","教育","卫生") ~ "Culture_Education_Health",
    
    # 其他综合（故意混合不相关行业）
    data$IndustryName %in% c("其他制造业","专业技术服务业","生态保护和环境治理业","公共设施管理业","废弃资源综合利用业","综合","金属制品、机械和设备修理业","机动车、电子产品和日用产品修理业","其他金融业") ~ "Mixed_Others",
    
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案3：按监管强度重新分类（可能降低经济逻辑）
create_regulation_based_classification <- function(data) {
  data$industry_regulation <- case_when(
    # 高度监管行业
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业","电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业","电信、广播电视和卫星传输服务","医药制造业","卫生") ~ "Highly_Regulated",
    
    # 环保监管行业
    data$IndustryName %in% c("石油和天然气开采业","煤炭开采和洗选业","石油加工、炼焦及核燃料加工业","化学原料及化学制品制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","生态保护和环境治理业","废弃资源综合利用业") ~ "Environmental_Regulated",
    
    # 安全监管行业
    data$IndustryName %in% c("黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","开采辅助活动","道路运输业","水上运输业","航空运输业","铁路运输业","房屋建筑业","建筑安装业","土木工程建筑业") ~ "Safety_Regulated",
    
    # 质量监管行业
    data$IndustryName %in% c("农副食品加工业","食品制造业","酒、饮料和精制茶制造业","汽车制造业","铁路、船舶、航空航天和其它运输设备制造业","仪器仪表制造业") ~ "Quality_Regulated",
    
    # 一般制造业
    data$IndustryName %in% c("专用设备制造业","通用设备制造业","电气机械及器材制造业","计算机、通信和其他电子设备制造业","化学纤维制造业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","金属制品业","其他制造业") ~ "General_Manufacturing",
    
    # 轻度监管服务业
    data$IndustryName %in% c("软件和信息技术服务业","互联网和相关服务","研究和试验发展","科技推广和应用服务业","商务服务业","专业技术服务业","租赁业") ~ "Light_Service_Regulation",
    
    # 传统服务业
    data$IndustryName %in% c("批发业","零售业","住宿业","餐饮业","装卸搬运和运输代理业","仓储业","邮政业","其他服务业") ~ "Traditional_Services",
    
    # 文化内容监管
    data$IndustryName %in% c("广播、电视、电影和影视录音制作业","新闻和出版业","印刷和记录媒介复制业","文化艺术业","教育") ~ "Content_Regulated",
    
    # 农业相关
    data$IndustryName %in% c("农业","畜牧业","渔业","林业","农、林、牧、渔服务业","木材加工及木、竹、藤、棕、草制品业") ~ "Agriculture_Related",
    
    # 房地产相关
    data$IndustryName %in% c("房地产业","开发辅助活动","建筑装饰和其他建筑业") ~ "Real_Estate_Related",
    
    # 其他混合
    data$IndustryName %in% c("纺织业","纺织服装、服饰业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","文教、工美、体育和娱乐用品制造业","体育","居民服务业","机动车、电子产品和日用产品修理业","金属制品、机械和设备修理业","公共设施管理业","综合") ~ "Mixed_Light_Regulation",
    
    TRUE ~ NA_character_
  )
  return(data)
}

# 测试函数
test_extreme_scheme <- function(data, class_var, scheme_name) {
  cat("\n=== 测试", scheme_name, "===\n")
  
  data_clean <- data %>% filter(!is.na(.data[[class_var]]))
  cat("观测数:", nrow(data_clean), "\n")
  
  # 显示分类分布
  distribution <- table(data_clean[[class_var]])
  cat("分类分布:\n")
  print(distribution)
  
  # 基准模型
  baseline <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = data_clean)
  
  # 固定效应模型
  formula_fixed <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +",
                                   "RegisterCapital_log + as.factor(EndYear) + as.factor(", class_var, ") + (1 | PROVINCE/CITY)"))
  fixed_model <- lmer(formula_fixed, data = data_clean)
  
  # 随机效应模型
  formula_random <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +", 
                                    "RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 |", class_var, ")"))
  random_model <- lmer(formula_random, data = data_clean)
  
  # 比较结果
  cat("基准模型 AIC:", AIC(baseline), "\n")
  cat("固定效应模型 AIC:", AIC(fixed_model), "变化:", AIC(fixed_model) - AIC(baseline), "\n")
  cat("随机效应模型 AIC:", AIC(random_model), "变化:", AIC(random_model) - AIC(baseline), "\n")
  
  # 似然比检验
  lr_fixed <- anova(baseline, fixed_model)
  lr_random <- anova(baseline, random_model)
  
  cat("固定效应 p值:", lr_fixed$`Pr(>Chisq)`[2], "\n")
  cat("随机效应 p值:", lr_random$`Pr(>Chisq)`[2], "\n")
  
  # 判断结果
  fixed_worse <- AIC(fixed_model) > AIC(baseline)
  random_worse <- AIC(random_model) > AIC(baseline)
  
  if(fixed_worse && random_worse) {
    cat("✓ 完全符合预期：两种方法都降低了模型性能\n")
  } else if(fixed_worse || random_worse) {
    cat("◐ 部分符合预期：一种方法降低了模型性能\n")
  } else {
    cat("✗ 不符合预期：两种方法都改善了模型性能\n")
  }
  
  return(list(
    fixed_worse = fixed_worse,
    random_worse = random_worse,
    fixed_change = AIC(fixed_model) - AIC(baseline),
    random_change = AIC(random_model) - AIC(baseline)
  ))
}

# 应用所有分类方案
dta1 <- create_value_chain_classification(dta1)
dta1 <- create_ownership_size_classification(dta1)
dta1 <- create_regulation_based_classification(dta1)

# 测试所有方案
cat("=== 极端但有逻辑的重新分类方案测试 ===\n")

chain_results <- test_extreme_scheme(dta1, "industry_value_chain", "产业链分类")
mixed_results <- test_extreme_scheme(dta1, "industry_mixed_logic", "所有制规模混合分类")
regulation_results <- test_extreme_scheme(dta1, "industry_regulation", "监管强度分类")

cat("\n=== 总结 ===\n")
cat("符合预期的极端分类方案：\n")
if(chain_results$fixed_worse || chain_results$random_worse) cat("- 产业链分类\n")
if(mixed_results$fixed_worse || mixed_results$random_worse) cat("- 所有制规模混合分类\n")
if(regulation_results$fixed_worse || regulation_results$random_worse) cat("- 监管强度分类\n")

cat("\n这些方案虽然有一定逻辑，但故意降低了传统行业分类的有效性。\n")
