---
title: "11类行业分类回归分析"
author: "ESG China Research"
date: "`r Sys.Date()`"
format: 
  html:
    toc: true
    toc-depth: 3
    code-fold: false
    theme: cosmo
editor: visual
---

# 数据准备

## 加载必要的库和数据

```{r setup, message=FALSE, warning=FALSE}
# 加载必要的库
library(plm)
library(lme4)
library(lmerTest)
library(stargazer)
library(sjPlot)
library(dplyr)
library(knitr)
library(kableExtra)
library(stringr)

# 加载数据
load("dta1_20240903.RData")

# 检查数据结构
cat("原始数据概览:\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")
```

## 创建11类行业分类

```{r industry_classification_11}
# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # 1 Energy
  dta1$IndustryName %in% c("石油和天然气开采业",
                           "石油加工、炼焦及核燃料加工业",
                           "煤炭开采和洗选业")                                   ~ "Energy",
  
  # 2 Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                           "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                           "木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业",
                           "金属制品业")                                         ~ "Materials",
  
  # 3 Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业",
                           "电气机械及器材制造业","通用设备制造业",
                           "装卸搬运和运输代理业","道路运输业","水上运输业",
                           "航空运输业","铁路运输业","仓储业","其他制造业",
                           "专业技术服务业","其他服务业",
                           "废弃资源综合利用业", "综合", "金属制品、机械和设备修理业",
                           "邮政业","印刷和记录媒介复制业",
                           # 建筑／环保
                           "房屋建筑业","建筑安装业","土木工程建筑业",
                           "建筑装饰和其他建筑业","生态保护和环境治理业",
                           "公共设施管理业")                                     ~ "Industrials",
  
  # 4 Consumer Discretionary
  dta1$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业",
                           "教育","纺织业","零售业","纺织服装、服饰业",
                           "文教、工美、体育和娱乐用品制造业","文化艺术业",
                           "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业",
                           "机动车、电子产品和日用产品修理业",
                           "汽车制造业")                                         ~ "Consumer Discretionary",
  
  # 5 Consumer Staples
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业",
                           "农业","畜牧业","渔业",
                           "农、林、牧、渔服务业")                               ~ "Consumer Staples",
  
  # 6 Health Care
  dta1$IndustryName %in% c("医药制造业","卫生")                                 ~ "Health Care",
  
  # 7 Financials
  dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业",
                           "其他金融业","租赁业","商务服务业")                   ~ "Financials",
  
  # 8 Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业",
                           "计算机、通信和其他电子设备制造业",
                           "仪器仪表制造业",
                           "研究和试验发展","科技推广和应用服务业")               ~ "Information Technology",
  
  # 9 Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务",
                           "广播、电视、电影和影视录音制作业",
                           "新闻和出版业","互联网和相关服务")                     ~ "Communication Services",
  
  # 10 Utilities
  dta1$IndustryName %in% c("电力、热力生产和供应业",
                           "燃气生产和供应业",
                           "水的生产和供应业")                                   ~ "Utilities",
  
  # 11 Real Estate
  dta1$IndustryName %in% c("房地产业","开发辅助活动")                            ~ "Real Estate",
  
  TRUE ~ NA_character_
)


# 显示分类结果
cat("11类分类结果:\n")
industry_table <- table(dta1$industry_type11, useNA = "always")
print(industry_table)

# 分析NA值的详细情况
na_data <- dta1 %>% filter(is.na(industry_type11))
cat("\n=== NA值分析 ===\n")
cat("NA观测数:", nrow(na_data), "\n")
cat("NA占总观测数的比例:", round(nrow(na_data)/nrow(dta1)*100, 2), "%\n")
cat("NA涉及的唯一公司数:", length(unique(na_data$Symbol)), "\n")
cat("NA涉及的唯一行业数:", length(unique(na_data$IndustryName)), "\n")

# 显示所有未分类的行业名称
cat("\n未分类的行业名称:\n")
na_industries <- na_data %>%
  group_by(IndustryName) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(desc(count))
print(na_industries)

# 创建去除NA的数据集用于分析
dta1_11class <- dta1 %>% filter(!is.na(industry_type11))
cat("\n去除NA后的观测数:", nrow(dta1_11class), "\n")
cat("去除NA后的公司数:", length(unique(dta1_11class$Symbol)), "\n")
```

# 混合效应模型 (LMER)

## 基础混合效应模型

```{r lmer_basic_11class_p3}
# 基础混合效应模型 - 省份随机效应
p3mix1_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1_11class)

p3mix2_11 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1_11class)

p3mix3_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1_11class)

# 使用tab_model输出结果
tab_model(p3mix1_11, p3mix2_11, p3mix3_11,
          title = "P3 Mixed Effects Models (11-Class) - Province Random Effects",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

```{r lmer_basic_11class_p4}
# 中央/地方政治关联的混合效应模型
p4mix1_11_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1_11class)

p4mix2_11_1 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                  (1 | PROVINCE), 
                  data = dta1_11class)

p4mix3_11_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1_11class)

p4mix4_11_1 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                  (1 | PROVINCE) ,
                  data = dta1_11class)
```

```{r}
# 输出结果

tab_model(p4mix1_11_1, p4mix2_11_1, p4mix3_11_1, p4mix4_11_1,
          title = "P4 Mixed Effects Models (11-Class) - Central vs Local Connections",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

## 包含行业随机效应的混合效应模型

```{r}
p3mix1_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE) + (0 + connection_num | industry_type11),
                       data = dta1_11class)

p3mix2_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE) + (0 + connection_num | industry_type11),
                       data = dta1_11class)

p3mix3_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE) + (0 + connection_num | industry_type11),
                       data = dta1_11class)


# 中央/地方政治关联的混合效应模型
p4mix1_11 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE) + (1 | industry_type11), 
                  data = dta1_11class)

p4mix2_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                  (1 | PROVINCE) + (0 + central_connection | industry_type11), 
                  data = dta1_11class)

p4mix3_11 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE) + (1 | industry_type11), 
                  data = dta1_11class)

p4mix4_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                  (1 | PROVINCE) + (0 + local_connection | industry_type11),
                  data = dta1_11class)


# 输出随机斜率模型结果
tab_model(p3mix1_slope_11, p3mix2_slope_11, p3mix3_slope_11,
          title = "Random Slope Models (11-Class) - Industry-specific Interaction Effects",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix1_11, p4mix2_11, p4mix3_11, p4mix4_11,
          title = "P4 Mixed Effects Models (11-Class) - Central vs Local Connections",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)


```

## 包含行业固定效应的混合效应模型

```{r lmer_fixed_industry_11class}
# 包含行业固定效应的混合效应模型
p3mix1_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE),
                                data = dta1_11class)

p3mix2_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE),
                                data = dta1_11class)


p3mix3_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                                   ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                                   as.factor(industry_type11) + (1 | PROVINCE),
                                   data = dta1_11class)

p4mix1_fixed_central_11 <- lmer(Environmental_Information_Disclosure ~ central_connection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               as.factor(industry_type11) + (1 | PROVINCE),
                               data = dta1_11class)

p4mix2_fixed_central_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               as.factor(industry_type11) + (1 | PROVINCE),
                               data = dta1_11class)

p4mix1_fixed_local_11 <- lmer(Environmental_Information_Disclosure ~ local_connection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             as.factor(industry_type11) + (1 | PROVINCE),
                             data = dta1_11class)
p4mix2_fixed_local_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             as.factor(industry_type11) + (1 | PROVINCE),
                             data = dta1_11class)

# 输出结果
tab_model(p3mix1_fixed_industry_11, p3mix2_fixed_industry_11, p3mix3_fixed_industry_11,
          title = "Mixed Effects Models with Industry Fixed Effects (11-Class) - P3 Models",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix1_fixed_central_11, p4mix2_fixed_central_11, p4mix1_fixed_local_11, p4mix2_fixed_local_11,
          title = "Mixed Effects Models with Industry Fixed Effects (11-Class) - P4 Models",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```