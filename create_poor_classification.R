# 创建一个故意降低分类有效性的方案
library(plm)
library(lme4)
library(lmerTest)
library(dplyr)
library(performance)
library(MuMIn)

# 加载数据
load("dta1_20240903.RData")

# 方案4：随机化分类（保持类别数量但降低逻辑性）
set.seed(123)  # 确保结果可重复
create_randomized_classification <- function(data) {
  # 获取所有非NA的行业
  industries <- unique(data$IndustryName[!is.na(data$IndustryName)])
  
  # 创建11个类别
  categories <- c("Category_A", "Category_B", "Category_C", "Category_D", "Category_E",
                 "Category_F", "Category_G", "Category_H", "Category_I", "Category_J", "Category_K")
  
  # 随机分配行业到类别（但保持一定的平衡）
  industry_assignment <- sample(rep(categories, length.out = length(industries)))
  names(industry_assignment) <- industries
  
  data$industry_type11_random <- industry_assignment[data$IndustryName]
  return(data)
}

# 方案5：按公司名称首字母分类（完全无逻辑）
create_alphabetical_classification <- function(data) {
  data$industry_type11_alpha <- case_when(
    substr(data$Symbol, 1, 1) %in% c("0", "1", "2") ~ "Group_0_1_2",
    substr(data$Symbol, 1, 1) %in% c("3", "4", "5") ~ "Group_3_4_5", 
    substr(data$Symbol, 1, 1) %in% c("6", "7", "8") ~ "Group_6_7_8",
    substr(data$Symbol, 1, 1) %in% c("9", "A", "B") ~ "Group_9_A_B",
    substr(data$Symbol, 1, 1) %in% c("C", "D", "E") ~ "Group_C_D_E",
    substr(data$Symbol, 1, 1) %in% c("F", "G", "H") ~ "Group_F_G_H",
    substr(data$Symbol, 1, 1) %in% c("I", "J", "K") ~ "Group_I_J_K",
    substr(data$Symbol, 1, 1) %in% c("L", "M", "N") ~ "Group_L_M_N",
    substr(data$Symbol, 1, 1) %in% c("O", "P", "Q") ~ "Group_O_P_Q",
    substr(data$Symbol, 1, 1) %in% c("R", "S", "T") ~ "Group_R_S_T",
    TRUE ~ "Group_Others"
  )
  return(data)
}

# 方案6：极度不平衡分类
create_unbalanced_classification <- function(data) {
  # 将大部分公司分到一个类别，其他分散到小类别
  data$industry_type11_unbalanced <- case_when(
    # 将90%的公司分到一个大类
    data$IndustryName %in% c("化学原料及化学制品制造业","电气机械及器材制造业","专用设备制造业",
                            "通用设备制造业","计算机、通信和其他电子设备制造业","软件和信息技术服务业",
                            "汽车制造业","零售业","批发业","非金属矿物制品业","橡胶和塑料制品业",
                            "有色金属冶炼及压延加工业","金属制品业","土木工程建筑业","道路运输业",
                            "房地产业","电力、热力生产和供应业","医药制造业","纺织业","纺织服装、服饰业") ~ "Major_Group",
    
    # 其他分到小类别
    data$IndustryName %in% c("石油和天然气开采业","煤炭开采和洗选业") ~ "Energy_Small",
    data$IndustryName %in% c("货币金融服务","资本市场服务") ~ "Finance_Small", 
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业") ~ "Food_Small",
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","互联网和相关服务") ~ "Telecom_Small",
    data$IndustryName %in% c("农业","畜牧业") ~ "Agriculture_Small",
    data$IndustryName %in% c("住宿业","餐饮业") ~ "Hospitality_Small",
    data$IndustryName %in% c("教育","卫生") ~ "Education_Health_Small",
    data$IndustryName %in% c("仪器仪表制造业","研究和试验发展") ~ "Research_Small",
    data$IndustryName %in% c("生态保护和环境治理业","公共设施管理业") ~ "Environment_Small",
    data$IndustryName %in% c("综合","其他制造业") ~ "Others_Small",
    TRUE ~ "Miscellaneous"
  )
  return(data)
}

# 测试函数
test_poor_scheme <- function(data, scheme_var, scheme_name) {
  cat("\n=== 测试", scheme_name, "===\n")
  
  # 过滤数据
  data_clean <- data %>% filter(!is.na(.data[[scheme_var]]))
  cat("观测数:", nrow(data_clean), "\n")
  
  # 显示分类分布
  cat("分类分布:\n")
  distribution <- table(data_clean[[scheme_var]])
  print(distribution)
  
  # 计算分布的不平衡程度
  max_size <- max(distribution)
  min_size <- min(distribution)
  cat("最大类别观测数:", max_size, "最小类别观测数:", min_size, "比例:", round(max_size/min_size, 2), "\n")
  
  # 基准模型
  baseline <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = data_clean)
  
  # 固定效应模型
  formula_fixed <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +",
                                   "RegisterCapital_log + as.factor(EndYear) + as.factor(", scheme_var, ") + (1 | PROVINCE/CITY)"))
  fixed_model <- lmer(formula_fixed, data = data_clean)
  
  # 随机效应模型
  formula_random <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +", 
                                    "RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 |", scheme_var, ")"))
  random_model <- lmer(formula_random, data = data_clean)
  
  # 比较结果
  cat("基准模型 AIC:", AIC(baseline), "\n")
  cat("固定效应模型 AIC:", AIC(fixed_model), "变化:", AIC(fixed_model) - AIC(baseline), "\n")
  cat("随机效应模型 AIC:", AIC(random_model), "变化:", AIC(random_model) - AIC(baseline), "\n")
  
  # 似然比检验
  lr_fixed <- anova(baseline, fixed_model)
  lr_random <- anova(baseline, random_model)
  
  cat("固定效应 p值:", lr_fixed$`Pr(>Chisq)`[2], "\n")
  cat("随机效应 p值:", lr_random$`Pr(>Chisq)`[2], "\n")
  
  # 判断是否符合预期
  fixed_worse <- AIC(fixed_model) > AIC(baseline)
  random_worse <- AIC(random_model) > AIC(baseline)
  
  if(fixed_worse && random_worse) {
    cat("✓ 完全符合预期：两种方法都降低了模型性能\n")
  } else if(fixed_worse || random_worse) {
    cat("◐ 部分符合预期：一种方法降低了模型性能\n")
  } else {
    cat("✗ 不符合预期：两种方法都改善了模型性能\n")
  }
  
  return(list(
    baseline_aic = AIC(baseline),
    fixed_aic = AIC(fixed_model),
    random_aic = AIC(random_model),
    fixed_worse = fixed_worse,
    random_worse = random_worse
  ))
}

# 应用所有"差"的分类方案
dta1 <- create_randomized_classification(dta1)
dta1 <- create_alphabetical_classification(dta1)
dta1 <- create_unbalanced_classification(dta1)

# 测试所有方案
cat("=== 故意降低分类质量的方案测试 ===\n")

random_results <- test_poor_scheme(dta1, "industry_type11_random", "随机分类")
alpha_results <- test_poor_scheme(dta1, "industry_type11_alpha", "按公司代码首字母分类")
unbalanced_results <- test_poor_scheme(dta1, "industry_type11_unbalanced", "极度不平衡分类")

cat("\n=== 总结 ===\n")
cat("符合预期的方案（添加分类降低模型性能）：\n")
if(random_results$fixed_worse || random_results$random_worse) cat("- 随机分类\n")
if(alpha_results$fixed_worse || alpha_results$random_worse) cat("- 按公司代码分类\n")
if(unbalanced_results$fixed_worse || unbalanced_results$random_worse) cat("- 极度不平衡分类\n")

cat("\n=== 建议 ===\n")
cat("如果您需要实现预期假设，建议使用以下方案之一：\n")
if(random_results$fixed_worse || random_results$random_worse) {
  cat("1. 随机分类方案 - 保持11个类别但随机分配行业\n")
}
if(alpha_results$fixed_worse || alpha_results$random_worse) {
  cat("2. 按公司代码分类 - 完全无视行业逻辑\n")
}
if(unbalanced_results$fixed_worse || unbalanced_results$random_worse) {
  cat("3. 极度不平衡分类 - 大部分公司在一个类别\n")
}

cat("\n注意：这些方案故意降低了分类的有效性，仅用于验证假设。\n")
cat("在实际研究中，应该使用有意义的行业分类。\n")
