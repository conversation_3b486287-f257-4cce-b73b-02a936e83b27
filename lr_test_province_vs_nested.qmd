---
title: "似然比检验：(1|PROVINCE) vs (1|PROVINCE/CITY) 随机效应结构比较"
author: "qcma"
date: "`r Sys.Date()`"
format: 
  html:
    toc: true
    toc-depth: 3
    code-fold: false
    theme: cosmo
editor: visual
---

通过似然比检验比较两种随机效应结构的拟合效果：

-   **(1\|PROVINCE)**：仅考虑省份层面的随机截距
-   **(1\|PROVINCE/CITY)**：考虑省份嵌套城市的层级结构

## 数据准备

```{r setup, message=FALSE, warning=FALSE}
# 加载必要的库
library(lme4)
library(lmerTest)
library(sjPlot)
library(dplyr)
library(knitr)
library(kableExtra)

# 加载数据
load("dta1_20240903.RData")
```

```{r create_industry_classification}
# 首先创建industry_type11变量（如果不存在）
if(!"industry_type11" %in% names(dta1)) {
  cat("创建industry_type11变量...\n")
  dta1$industry_type11 <- case_when(
    # 1 Energy
    dta1$IndustryName %in% c("石油和天然气开采业",
                             "石油加工、炼焦及核燃料加工业",
                             "煤炭开采和洗选业")                                   ~ "Energy",
    
    # 2 Materials
    dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                             "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                             "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                             "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                             "木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业",
                             "金属制品业")                                         ~ "Materials",
    
    # 3 Industrials
    dta1$IndustryName %in% c("通用设备制造业","专用设备制造业","汽车制造业",
                             "铁路、船舶、航空航天和其他运输设备制造业",
                             "电气机械及器材制造业","仪器仪表制造业",
                             "道路运输业","水上运输业","航空运输业","管道运输业",
                             "装卸搬运和运输代理业","仓储业","商务服务业",
                             "房屋建筑业","土木工程建筑业","建筑安装业",
                             "建筑装饰和其他建筑业")                               ~ "Industrials",
    
    # 4 Consumer Discretionary
    dta1$IndustryName %in% c("纺织业","纺织服装、服饰业",
                             "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业",
                             "文教、工美、体育和娱乐用品制造业","酒、饮料和精制茶制造业",
                             "批发业","零售业","住宿业","餐饮业",
                             "文化艺术业","体育","娱乐业")                         ~ "Consumer Discretionary",
    
    # 5 Consumer Staples
    dta1$IndustryName %in% c("农业","林业","畜牧业","渔业","农、林、牧、渔服务业",
                             "农副食品加工业","食品制造业","烟草制品业")             ~ "Consumer Staples",
    
    # 6 Health Care
    dta1$IndustryName %in% c("医药制造业","卫生","社会工作")                       ~ "Health Care",
    
    # 7 Financials
    dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业",
                             "其他金融业")                                         ~ "Financials",
    
    # 8 Information Technology
    dta1$IndustryName %in% c("计算机、通信和其他电子设备制造业",
                             "软件和信息技术服务业","研究和试验发展")               ~ "Information Technology",
    
    # 9 Communication Services
    dta1$IndustryName %in% c("电信、广播电视和卫星传输服务",
                             "互联网和相关服务","广播、电视、电影和影视录音制作业",
                             "新闻和出版业","邮政业")                               ~ "Communication Services",
    
    # 10 Utilities
    dta1$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业",
                             "水的生产和供应业","生态保护和环境治理业",
                             "公共设施管理业")                                     ~ "Utilities",
    
    # 11 Real Estate
    dta1$IndustryName %in% c("房地产业")                                         ~ "Real Estate"
  )
}

# 创建去除NA的数据集用于分析
dta1_11class <- dta1 %>% filter(!is.na(industry_type11))
cat("去除NA后的观测数:", nrow(dta1_11class), "\n")
cat("去除NA后的公司数:", length(unique(dta1_11class$Symbol)), "\n")
```

## P3模型组：(1\|PROVINCE) vs (1\|PROVINCE/CITY)

### P3模型组 - (1\|PROVINCE)结构

```{r p3_province_models}
cat("构建P3模型组 - (1|PROVINCE)结构...\n")

# P3模型1 - 仅省份随机效应
p3mix1_province <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                       RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE),
                       data = dta1_11class, REML = FALSE)

# P3模型2 - 仅省份随机效应
p3mix2_province <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                       RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE),
                       data = dta1_11class, REML = FALSE)

# P3模型3 - 仅省份随机效应
p3mix3_province <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE),
                       data = dta1_11class, REML = FALSE)

cat("P3模型组 - (1|PROVINCE)结构构建完成\n")
```

### P3模型组 - (1\|PROVINCE/CITY)嵌套结构

```{r p3_nested_models}
cat("构建P3模型组 - (1|PROVINCE/CITY)嵌套结构...\n")

# P3模型1 - 省份嵌套城市随机效应
p3mix1_nested <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                     RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                     data = dta1_11class)

# P3模型2 - 省份嵌套城市随机效应
p3mix2_nested <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
                     RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                     data = dta1_11class)

# P3模型3 - 省份嵌套城市随机效应
p3mix3_nested <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                     ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                     data = dta1_11class)

cat("P3模型组 - (1|PROVINCE/CITY)嵌套结构构建完成\n")
```

## P4模型组：(1\|PROVINCE) vs (1\|PROVINCE/CITY)

### P4模型组 - (1\|PROVINCE)结构

```{r p4_province_models}
cat("构建P4模型组 - (1|PROVINCE)结构...\n")

# P4模型1 - 仅省份随机效应
p4mix1_province <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                       RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE),
                       data = dta1_11class, REML = FALSE)

# P4模型2 - 仅省份随机效应
p4mix2_province <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE),
                       data = dta1_11class, REML = FALSE)

# P4模型3 - 仅省份随机效应
p4mix3_province <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                       RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE),
                       data = dta1_11class, REML = FALSE)

# P4模型4 - 仅省份随机效应
p4mix4_province <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE),
                       data = dta1_11class, REML = FALSE)

cat("P4模型组 - (1|PROVINCE)结构构建完成\n")
```

### P4模型组 - (1\|PROVINCE/CITY)嵌套结构

```{r p4_nested_models}
cat("构建P4模型组 - (1|PROVINCE/CITY)嵌套结构...\n")

# P4模型1 - 省份嵌套城市随机效应
p4mix1_nested <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                     data = dta1_11class, REML = FALSE)

# P4模型2 - 省份嵌套城市随机效应
p4mix2_nested <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                     (1 | PROVINCE/CITY),
                     data = dta1_11class, REML = FALSE)

# P4模型3 - 省份嵌套城市随机效应
p4mix3_nested <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                     data = dta1_11class, REML = FALSE)

# P4模型4 - 省份嵌套城市随机效应
p4mix4_nested <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                     (1 | PROVINCE/CITY),
                     data = dta1_11class, REML = FALSE)

cat("P4模型组 - (1|PROVINCE/CITY)嵌套结构构建完成\n")
```

## 似然比检验

### P3模型组似然比检验

```{r lr_test_p3}
cat("=== P3模型组似然比检验 ===\n")

# P3模型1的似然比检验
cat("\n--- P3模型1：Age + connection_num ---\n")
lr_test_p3_1 <- anova(p3mix1_province, p3mix1_nested)
print(lr_test_p3_1)

# P3模型2的似然比检验
cat("\n--- P3模型2：after_first_inspection ---\n")
lr_test_p3_2 <- anova(p3mix2_province, p3mix2_nested)
print(lr_test_p3_2)

# P3模型3的似然比检验
cat("\n--- P3模型3：Age + after_first_inspection * connection_num ---\n")
lr_test_p3_3 <- anova(p3mix3_province, p3mix3_nested)
print(lr_test_p3_3)
```

### P4模型组似然比检验

```{r lr_test_p4}
cat("=== P4模型组似然比检验 ===\n")

# P4模型1的似然比检验
cat("\n--- P4模型1：Age + central_connection ---\n")
lr_test_p4_1 <- anova(p4mix1_province, p4mix1_nested)
print(lr_test_p4_1)

# P4模型2的似然比检验
cat("\n--- P4模型2：central_connection * after_first_inspection ---\n")
lr_test_p4_2 <- anova(p4mix2_province, p4mix2_nested)
print(lr_test_p4_2)

# P4模型3的似然比检验
cat("\n--- P4模型3：Age + local_connection ---\n")
lr_test_p4_3 <- anova(p4mix3_province, p4mix3_nested)
print(lr_test_p4_3)

# P4模型4的似然比检验
cat("\n--- P4模型4：local_connection * after_first_inspection ---\n")
lr_test_p4_4 <- anova(p4mix4_province, p4mix4_nested)
print(lr_test_p4_4)
```

## 模型拟合指标比较

```{r model_comparison}
cat("=== 模型拟合指标比较 ===\n")

# 提取关键指标
extract_model_info <- function(model, model_name) {
  # 由于使用ML拟合，可以直接使用deviance
  data.frame(
    Model = model_name,
    AIC = AIC(model),
    BIC = BIC(model),
    LogLik = as.numeric(logLik(model)),
    Deviance = deviance(model),
    df = attr(logLik(model), "df")
  )
}

# 创建比较表
comparison_table <- rbind(
  # P3模型组
  extract_model_info(p3mix1_province, "P3Mix1_Province"),
  extract_model_info(p3mix1_nested, "P3Mix1_Nested"),
  extract_model_info(p3mix2_province, "P3Mix2_Province"),
  extract_model_info(p3mix2_nested, "P3Mix2_Nested"),
  extract_model_info(p3mix3_province, "P3Mix3_Province"),
  extract_model_info(p3mix3_nested, "P3Mix3_Nested"),
  # P4模型组
  extract_model_info(p4mix1_province, "P4Mix1_Province"),
  extract_model_info(p4mix1_nested, "P4Mix1_Nested"),
  extract_model_info(p4mix2_province, "P4Mix2_Province"),
  extract_model_info(p4mix2_nested, "P4Mix2_Nested"),
  extract_model_info(p4mix3_province, "P4Mix3_Province"),
  extract_model_info(p4mix3_nested, "P4Mix3_Nested"),
  extract_model_info(p4mix4_province, "P4Mix4_Province"),
  extract_model_info(p4mix4_nested, "P4Mix4_Nested")
)

# 使用kable美化表格
comparison_table %>%
  kable(digits = 2, caption = "模型拟合指标比较表") %>%
  kable_styling(bootstrap_options = c("striped", "hover", "condensed", "responsive")) %>%
  row_spec(c(2,4,6,8,10,12,14), background = "#f0f8ff")  # 嵌套模型行高亮
```

## 模型结果表格

### P3模型组结果

#### P3模型组 - (1\|PROVINCE)结构

```{r p3_province_results}
tab_model(p3mix1_province, p3mix2_province, p3mix3_province,
          title = "P3 Mixed Effects Models - (1|PROVINCE) Structure",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

#### P3模型组 - (1\|PROVINCE/CITY)嵌套结构

```{r p3_nested_results}
tab_model(p3mix1_nested, p3mix2_nested, p3mix3_nested,
          title = "P3 Mixed Effects Models - (1|PROVINCE/CITY) Nested Structure",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

### P4模型组结果

#### P4模型组 - (1\|PROVINCE)结构

```{r p4_province_results}
tab_model(p4mix1_province, p4mix2_province, p4mix3_province, p4mix4_province,
          title = "P4 Mixed Effects Models - (1|PROVINCE) Structure",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

#### P4模型组 - (1\|PROVINCE/CITY)嵌套结构

```{r p4_nested_results}
tab_model(p4mix1_nested, p4mix2_nested, p4mix3_nested, p4mix4_nested,
          title = "P4 Mixed Effects Models - (1|PROVINCE/CITY) Nested Structure",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

## 结果解释和总结

### 核心发现

所有P3和P4模型的似然比检验结果一致显示：**(1\|PROVINCE/CITY)嵌套结构显著优于(1\|PROVINCE)简单结构**，所有检验的p值均 \< 2.2e-16 (\*\*\*), 达到极高的统计显著性水平。

### 具体检验结果

```{r extract_lr_results, echo=FALSE}
# 提取似然比检验结果
extract_lr_stats <- function(lr_test) {
  chisq <- round(lr_test$Chisq[2], 2)
  p_value <- lr_test$`Pr(>Chisq)`[2]
  p_text <- if(p_value < 2.2e-16) "< 2.2e-16" else format(p_value, scientific = TRUE)
  return(list(chisq = chisq, p_value = p_text))
}

# P3模型组结果
p3_1_stats <- extract_lr_stats(lr_test_p3_1)
p3_2_stats <- extract_lr_stats(lr_test_p3_2)
p3_3_stats <- extract_lr_stats(lr_test_p3_3)

# P4模型组结果
p4_1_stats <- extract_lr_stats(lr_test_p4_1)
p4_2_stats <- extract_lr_stats(lr_test_p4_2)
p4_3_stats <- extract_lr_stats(lr_test_p4_3)
p4_4_stats <- extract_lr_stats(lr_test_p4_4)

cat("**P3模型组：**\n")
cat("- P3模型1 (Age + connection_num): χ² =", p3_1_stats$chisq, ", p", p3_1_stats$p_value, "***\n")
cat("- P3模型2 (after_first_inspection): χ² =", p3_2_stats$chisq, ", p", p3_2_stats$p_value, "***\n")
cat("- P3模型3 (Age + after_first_inspection * connection_num): χ² =", p3_3_stats$chisq, ", p", p3_3_stats$p_value, "***\n\n")

cat("**P4模型组：**\n")
cat("- P4模型1 (Age + central_connection): χ² =", p4_1_stats$chisq, ", p", p4_1_stats$p_value, "***\n")
cat("- P4模型2 (central_connection * after_first_inspection): χ² =", p4_2_stats$chisq, ", p", p4_2_stats$p_value, "***\n")
cat("- P4模型3 (Age + local_connection): χ² =", p4_3_stats$chisq, ", p", p4_3_stats$p_value, "***\n")
cat("- P4模型4 (local_connection * after_first_inspection): χ² =", p4_4_stats$chisq, ", p", p4_4_stats$p_value, "***\n")
```

### 模型拟合改善程度

嵌套结构相比简单省份结构的改善： - **AIC降低约190-192点** (越低越好) - **BIC降低约183-185点** (越低越好) - **对数似然值提高约96-97点** (越高越好) - **偏差(Deviance)降低约190-192点** (越低越好)

### 统计学意义

1.  **嵌套关系验证**：(1\|PROVINCE/CITY)是(1\|PROVINCE)的扩展，符合LR检验的嵌套要求
2.  **显著性水平**：所有检验p值均远小于0.001，达到极高显著性
3.  **效应量**：χ²值均在191-195之间，表明城市层级效应的实质性影响
4.  **一致性**：七个不同规格的模型均得出相同结论，结果稳健

### 实质性解释

#### 1. 城市层级的重要性：

-   同一省份内不同城市间存在显著的环境信息披露差异
-   仅考虑省份层面会忽略重要的城市内变异
-   城市特征对企业环境行为有独立的解释力

#### 2. 模型选择建议：

-   强烈建议采用(1\|PROVINCE/CITY)嵌套结构
-   该结构能更准确地捕捉中国行政层级的影响
-   提供更精确的参数估计和推断

#### 3. 理论意义：

-   支持多层级治理理论：省份和城市层面都有独立影响
-   验证了中国环境治理的层级化特征
-   为政策制定提供更精细的地理单元参考

### 回应审稿人关切

1.  **LR检验要求**：已满足嵌套模型比较的统计要求
2.  **模型复杂度**：虽然增加了一个参数，但显著改善了拟合效果
3.  **实证支持**：提供了选择嵌套结构的强有力统计证据
4.  **稳健性**：多个模型规格均支持相同结论

### 结论

**(1\|PROVINCE/CITY)嵌套结构在统计上和实质上均显著优于(1\|PROVINCE)结构**，为研究中采用更复杂的嵌套随机效应结构提供了充分的实证依据。