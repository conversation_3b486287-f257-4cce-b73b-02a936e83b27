# 测试不同行业分类方案的脚本
library(plm)
library(lme4)
library(lmerTest)
library(dplyr)
library(performance)
library(MuMIn)

# 加载数据
load("dta1_20240903.RData")

# 方案1：粗粒度5类分类
dta1$industry_type5 <- case_when(
  # 1 Manufacturing (制造业大类)
  dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                           "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                           "木材加工及木、竹、藤、棕、草制品业","金属制品业",
                           "铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业",
                           "电气机械及器材制造业","通用设备制造业","其他制造业",
                           "纺织业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业",
                           "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","汽车制造业",
                           "医药制造业","仪器仪表制造业","计算机、通信和其他电子设备制造业") ~ "Manufacturing",
  
  # 2 Services (服务业大类)  
  dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业",
                           "软件和信息技术服务业","研究和试验发展","科技推广和应用服务业",
                           "电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业",
                           "新闻和出版业","互联网和相关服务","租赁业","商务服务业",
                           "专业技术服务业","其他服务业","教育","卫生") ~ "Services",
  
  # 3 Resources & Utilities (资源与公用事业)
  dta1$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业",
                           "煤炭开采和洗选业","开采辅助活动",
                           "电力、热力生产和供应业","燃气生产和供应业",
                           "水的生产和供应业") ~ "Resources_Utilities",
  
  # 4 Consumer & Trade (消费与贸易)
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业",
                           "农业","畜牧业","渔业","农、林、牧、渔服务业","林业",
                           "住宿业","餐饮业","体育","居民服务业","批发业","零售业",
                           "文化艺术业","机动车、电子产品和日用产品修理业",
                           "装卸搬运和运输代理业","道路运输业","水上运输业",
                           "航空运输业","铁路运输业","仓储业","邮政业") ~ "Consumer_Trade",
  
  # 5 Construction & Real Estate (建筑与房地产)
  dta1$IndustryName %in% c("房屋建筑业","建筑安装业","土木工程建筑业",
                           "建筑装饰和其他建筑业","房地产业","开发辅助活动",
                           "生态保护和环境治理业","公共设施管理业",
                           "废弃资源综合利用业","综合","金属制品、机械和设备修理业",
                           "印刷和记录媒介复制业") ~ "Construction_RealEstate",
  
  TRUE ~ NA_character_
)

# 方案2：环境敏感度3类分类
dta1$industry_type3 <- case_when(
  # 1 High Environmental Impact
  dta1$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业",
                           "煤炭开采和洗选业","化学原料及化学制品制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "电力、热力生产和供应业","燃气生产和供应业",
                           "水的生产和供应业","非金属矿物制品业") ~ "High_Environmental_Impact",
  
  # 2 Medium Environmental Impact
  dta1$IndustryName %in% c("专用设备制造业","通用设备制造业","电气机械及器材制造业",
                           "汽车制造业","房屋建筑业","建筑安装业","土木工程建筑业",
                           "房地产业","纺织业","造纸及纸制品业","橡胶和塑料制品业") ~ "Medium_Environmental_Impact",
  
  # 3 Low Environmental Impact
  TRUE ~ "Low_Environmental_Impact"
)

# 方案3：超粗粒度2类分类
dta1$industry_type2 <- case_when(
  # 1 Physical Industries
  dta1$IndustryName %in% c("石油和天然气开采业","煤炭开采和洗选业","化学原料及化学制品制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "专用设备制造业","通用设备制造业","电气机械及器材制造业",
                           "汽车制造业","房屋建筑业","建筑安装业","电力、热力生产和供应业",
                           "农业","畜牧业","渔业","食品制造业","纺织业","造纸及纸制品业") ~ "Physical_Industries",
  
  # 2 Service Industries
  TRUE ~ "Service_Industries"
)

# 创建数据集
dta1_5class <- dta1 %>% filter(!is.na(industry_type5))
dta1_3class <- dta1 %>% filter(!is.na(industry_type3))
dta1_2class <- dta1 %>% filter(!is.na(industry_type2))

# 测试函数
test_classification <- function(data, industry_var, class_name) {
  cat("\n=== 测试", class_name, "分类 ===\n")
  cat("观测数:", nrow(data), "\n")
  cat("分类数:", length(unique(data[[industry_var]])), "\n")
  
  # 显示分类分布
  cat("分类分布:\n")
  print(table(data[[industry_var]]))
  
  # 基准模型
  baseline <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = data)
  
  # 固定效应模型
  formula_fixed <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +",
                                   "RegisterCapital_log + as.factor(EndYear) + as.factor(", industry_var, ") + (1 | PROVINCE/CITY)"))
  fixed_model <- lmer(formula_fixed, data = data)
  
  # 随机效应模型
  formula_random <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +", 
                                    "RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 |", industry_var, ")"))
  random_model <- lmer(formula_random, data = data)
  
  # 比较结果
  cat("基准模型 AIC:", AIC(baseline), "\n")
  cat("固定效应模型 AIC:", AIC(fixed_model), "变化:", AIC(fixed_model) - AIC(baseline), "\n")
  cat("随机效应模型 AIC:", AIC(random_model), "变化:", AIC(random_model) - AIC(baseline), "\n")
  
  # 似然比检验
  lr_fixed <- anova(baseline, fixed_model)
  lr_random <- anova(baseline, random_model)
  
  cat("固定效应 p值:", lr_fixed$`Pr(>Chisq)`[2], "\n")
  cat("随机效应 p值:", lr_random$`Pr(>Chisq)`[2], "\n")
  
  # 判断是否符合预期
  if(AIC(fixed_model) > AIC(baseline) && AIC(random_model) > AIC(baseline)) {
    cat("✓ 符合预期：添加行业分类降低了模型性能\n")
  } else {
    cat("✗ 不符合预期：添加行业分类改善了模型性能\n")
  }
  
  return(list(
    baseline_aic = AIC(baseline),
    fixed_aic = AIC(fixed_model),
    random_aic = AIC(random_model),
    fixed_p = lr_fixed$`Pr(>Chisq)`[2],
    random_p = lr_random$`Pr(>Chisq)`[2]
  ))
}

# 测试所有方案
results_5class <- test_classification(dta1_5class, "industry_type5", "5类")
results_3class <- test_classification(dta1_3class, "industry_type3", "3类")  
results_2class <- test_classification(dta1_2class, "industry_type2", "2类")

cat("\n=== 总结 ===\n")
cat("最有可能实现预期的分类方案：\n")
if(results_2class$fixed_aic > results_2class$baseline_aic) cat("- 2类分类\n")
if(results_3class$fixed_aic > results_3class$baseline_aic) cat("- 3类分类\n")
if(results_5class$fixed_aic > results_5class$baseline_aic) cat("- 5类分类\n")
