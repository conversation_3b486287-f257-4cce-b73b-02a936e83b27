# 随机斜率模型位置指南

## 在HTML文件中的位置

### 11类分析文件 (`industry_11class_analysis.html`)

随机斜率模型位于以下章节：

1. **章节**: "包含行业随机效应的混合效应模型"
2. **具体位置**: 在P4混合效应模型表格之后
3. **表格标题**: "Random Slope Models (11-Class) - Industry-specific Interaction Effects"
4. **包含的模型**:
   - P3 随机斜率模型 (connection_num的行业随机斜率)
   - P4 中央随机斜率模型 (central_connection的行业随机斜率)
   - P4 地方随机斜率模型 (local_connection的行业随机斜率)

### 13类分析文件 (`industry_13class_analysis.html`)

随机斜率模型位于以下章节：

1. **章节**: "包含行业随机效应的混合效应模型"
2. **具体位置**: 在P4混合效应模型表格之后
3. **表格标题**: "Random Slope Models (13-Class) - Industry-specific Interaction Effects"
4. **包含的模型**:
   - P3 随机斜率模型 (connection_num的行业随机斜率)
   - P4 中央随机斜率模型 (central_connection的行业随机斜率)
   - P4 地方随机斜率模型 (local_connection的行业随机斜率)

## 如何在浏览器中快速找到

### 方法1: 使用浏览器搜索功能
1. 在浏览器中按 `Ctrl+F` (Windows) 或 `Cmd+F` (Mac)
2. 搜索关键词: "Random Slope"
3. 会直接跳转到随机斜率模型表格

### 方法2: 使用目录导航
1. 在HTML文件顶部有目录 (Table of Contents)
2. 点击 "混合效应模型 (LMER)" 
3. 然后点击 "包含行业随机效应的混合效应模型"
4. 向下滚动找到 "Random Slope Models" 表格

### 方法3: 按章节顺序浏览
1. 固定效应模型 (PLM)
2. 混合效应模型 (LMER)
   - 基础混合效应模型
   - **包含行业随机效应的混合效应模型** ← 随机斜率模型在这里
   - 多层级随机效应模型
3. 模型比较与分析

## 模型特点说明

### 随机效应结构
- **省份**: `(1 | PROVINCE)` - 随机截距
- **行业**: `(0 + variable | industry)` - 随机斜率

### 模型解释
这些模型允许政治关联变量的效应在不同行业间有不同的斜率，而省份层面只有不同的截距。

### 表格内容
每个表格包含：
- 固定效应系数
- 随机效应方差
- 模型拟合指标 (R²边际、R²条件、ICC)
- 观测数量

## 模型性能指标位置

在 "模型比较与分析" 章节中，您还可以找到：
- 随机斜率模型的R²指标
- 与其他模型的性能比较
- ICC (组内相关系数) 分析

## 注意事项

1. 随机斜率模型比随机截距模型更复杂，需要更多的计算资源
2. 模型收敛可能需要更长时间
3. 如果遇到收敛问题，我们使用了简化版本的随机斜率（单个变量而非交互项）

现在您应该能够在HTML文件中找到随机斜率模型的完整结果了！
