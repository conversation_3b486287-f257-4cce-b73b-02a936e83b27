---
title: "13类行业分类回归分析"
author: "ESG China Research"
date: "`r Sys.Date()`"
format: 
  html:
    toc: true
    toc-depth: 3
    code-fold: false
    theme: cosmo
editor: visual
---

# 数据准备

## 加载必要的库和数据

```{r setup, message=FALSE, warning=FALSE}
# 加载必要的库
library(plm)
library(lme4)
library(lmerTest)
library(stargazer)
library(sjPlot)
library(performance)
library(dplyr)
library(knitr)
library(kableExtra)

# 加载数据
load("dta1_20240903.RData")

# 检查数据结构
cat("原始数据概览:\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")
```

## 创建13类行业分类

```{r industry_classification_13}
# 创建13类行业分类
dta1$industry13 <- case_when(
  # 1. Agriculture, Forestry, Livestock Farming, Fishery
  dta1$IndustryName %in% c("农业", "林业", "畜牧业", "渔业", 
                           "农、林、牧、渔服务业") ~ "Agriculture, Forestry, Livestock Farming, Fishery",
  
  # 2. Mining
  dta1$IndustryName %in% c("煤炭开采和洗选业", "石油和天然气开采业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "开采辅助活动") ~ "Mining",
  
  # 3. Manufacturing
  dta1$IndustryName %in% c("医药制造业", "化学原料及化学制品制造业", 
                           "化学纤维制造业", "非金属矿物制品业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "金属制品业", "通用设备制造业", 
                           "专用设备制造业", "铁路、船舶、航空航天和其它运输设备制造业", 
                           "汽车制造业", "电气机械及器材制造业", 
                           "计算机、通信和其他电子设备制造业", "仪器仪表制造业", 
                           "其他制造业", "食品制造业", 
                           "酒、饮料和精制茶制造业", "农副食品加工业", 
                           "纺织业", "纺织服装、服饰业", 
                           "皮革、毛皮、羽毛及其制品和制鞋业", "木材加工及木、竹、藤、棕、草制品业", 
                           "造纸及纸制品业", "印刷和记录媒介复制业", 
                           "橡胶和塑料制品业", "家具制造业", 
                           "废弃资源综合利用业", "文教、工美、体育和娱乐用品制造业", 
                           "金属制品、机械和设备修理业", "石油加工、炼焦及核燃料加工业") ~ "Manufacturing",
  
  # 4. Electric Power, Gas, and Water Production and Supply
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "水的生产和供应业") ~ "Electric Power, Gas, and Water Production and Supply",
  
  # 5. Construction
  dta1$IndustryName %in% c("房屋建筑业", "建筑安装业", 
                           "土木工程建筑业", "建筑装饰和其他建筑业") ~ "Construction",
  
  # 6. Transport and Storage
  dta1$IndustryName %in% c("装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "铁路运输业", 
                           "航空运输业", "仓储业") ~ "Transport and Storage",
  
  # 7. Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "专业技术服务业","互联网和相关服务", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # 8. Wholesale and Retail Trade
  dta1$IndustryName %in% c("批发业", "零售业") ~ "Wholesale and Retail Trade",
  
  # 9. Finance and Insurance
  dta1$IndustryName %in% c("货币金融服务", "资本市场服务", 
                           "其他金融业", "保险业"
                           ) ~ "Finance and Insurance",
  
  # 10. Real Estate
  dta1$IndustryName %in% c("租赁业", "房地产业") ~ "Real Estate",
  
  # 11. Social Service
  dta1$IndustryName %in% c("教育", "卫生", 
                           "公共设施管理业", "生态保护和环境治理业", 
                           "居民服务业") ~ "Social Service",
  
  # 12. Communication and Culture
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "文化艺术业", 
                           "体育", "邮政业") ~ "Communication and Culture",
  
  # 13. Others
  TRUE ~ "Others"
)

# 显示分类结果
cat("13类分类结果:\n")
table(dta1$industry13, useNA = "always")

cat("\n13类分类覆盖率: 100% (无NA值)\n")
cat("总观测数:", nrow(dta1), "\n")
cat("总公司数:", length(unique(dta1$Symbol)), "\n")
```

# 固定效应模型 (PLM)

## P3: 政治关联（连续变量）的影响

```{r p3_plm_13class}
# P3 模型 - 使用13类行业分类
p3way1_13 <- plm(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + 
                 Leverage + as.factor(industry13) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

p3way2_13 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + 
                 as.factor(industry13) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

p3way3_13 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                 ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

# 输出结果
stargazer(p3way1_13, p3way2_13, p3way3_13,
          type="text", 
          column.labels = c("Model 1", "Model 2", "Model 3"),
          title="P3 Models with 13-Class Industry Classification (PLM)",
          omit="as.factor",
          notes=c("Industry fixed effects (13 classes) included"),
          dep.var.labels = "Environmental Information Disclosure")
```

## P4: 中央与地方政治关联（连续变量）的影响

```{r p4_plm_13class}
# P4 模型 - 使用13类行业分类
p4m1_13 <- plm(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + 
               RegisterCapital_log + as.factor(industry13) + as.factor(PROVINCE) + 
               ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m2_13 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m3_13 <- plm(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + 
               RegisterCapital_log + as.factor(industry13) + as.factor(PROVINCE) + 
               ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m4_13 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

# 输出结果
stargazer(p4m1_13, p4m2_13, p4m3_13, p4m4_13,
          type="text", 
          column.labels = c("Central: Main", "Central: Interaction", "Local: Main", "Local: Interaction"),
          title="P4 Models with 13-Class Industry Classification (PLM)",
          omit="as.factor",
          notes=c("Industry fixed effects (13 classes) included"),
          dep.var.labels = "Environmental Information Disclosure")
```

# 混合效应模型 (LMER)

## 基础混合效应模型

```{r lmer_basic_13class}
# 基础混合效应模型 - 省份随机效应
p3mix1_13 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1)

p3mix2_13 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1)

p3mix3_13 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE), 
                  data = dta1)

# 使用tab_model输出结果
tab_model(p3mix1_13, p3mix2_13, p3mix3_13,
          title = "P3 Mixed Effects Models (13-Class) - Province Random Effects",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

## 包含行业随机效应的混合效应模型

```{r lmer_industry_13class}
# 包含行业随机效应的模型
p3mix4_13 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE) + (1 | industry13), 
                  data = dta1)

p3mix5_13 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                  (1 | PROVINCE) + (1 | industry13), 
                  data = dta1)

# 中央/地方政治关联的混合效应模型
p4mix1_13 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE) + (1 | industry13), 
                  data = dta1)

p4mix2_13 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                  (1 | PROVINCE) + (1 | industry13), 
                  data = dta1)

p4mix3_13 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE) + (1 | industry13), 
                  data = dta1)

p4mix4_13 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                  (1 | PROVINCE) + (1 | industry13),
                  data = dta1)

# 添加随机斜率模型 - 行业层面的交互效应随机斜率
p3mix_slope_13 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE) + (0 + after_first_inspection * connection_num | industry13),
                       data = dta1)

p4mix_central_slope_13 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               (1 | PROVINCE) + (0 + central_connection * after_first_inspection | industry13),
                               data = dta1)

p4mix_local_slope_13 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             (1 | PROVINCE) + (0 + local_connection * after_first_inspection | industry13),
                             data = dta1)

# 输出结果
tab_model(p3mix4_13, p3mix5_13,
          title = "P3 Mixed Effects Models (13-Class) - Province + Industry Random Effects",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix1_13, p4mix2_13, p4mix3_13, p4mix4_13,
          title = "P4 Mixed Effects Models (13-Class) - Central vs Local Connections",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

# 输出随机斜率模型结果
tab_model(p3mix_slope_13, p4mix_central_slope_13, p4mix_local_slope_13,
          title = "Random Slope Models (13-Class) - Industry-specific Interaction Effects",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

## 多层级随机效应模型

```{r lmer_multilevel_13class}
# 城市随机效应模型
p3mix_city_13 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage +
                      RegisterCapital_log + (1 | CITY),
                      data = dta1)

# 省份+城市随机效应模型
p3mix_prov_city_13 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage +
                           RegisterCapital_log + (1 | PROVINCE) + (1 | CITY),
                           data = dta1)

# 嵌套随机效应模型
p3mix1_nested_13 <- lmer(Environmental_Information_Disclosure ~ connection_num + ROA + ESG_Rate + Leverage +
                        RegisterCapital_log + (1 | PROVINCE/CITY),
                        data = dta1)

p3mix2_nested_13 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ROA + ESG_Rate + Leverage +
                        RegisterCapital_log + (1 | PROVINCE/CITY),
                        data = dta1)
p3mix3_nested_13 <- lmer(Environmental_Information_Disclosure ~ connection_num * after_first_inspection + ROA + ESG_Rate + Leverage +
                        RegisterCapital_log + (1 | PROVINCE/CITY),
                        data = dta1)

# 多层级随机效应模型（包含行业）
p3mix_multi_industry_13 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage +
                                RegisterCapital_log + (1 | PROVINCE) + (1 | CITY) + (1 | industry13),
                                data = dta1)

p3mix_nested_industry_13 <- lmer(Environmental_Information_Disclosure ~ connection_num + ROA + ESG_Rate + Leverage +
                                 RegisterCapital_log + (1 | PROVINCE/CITY) + (1 | industry13),
                                 data = dta1)

# 包含行业的复杂模型
p3mix_complex_13 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                         ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                         (1 | PROVINCE) + (1 | industry13),
                         data = dta1)

# 输出结果

tab_model(p3mix1_nested_13, p3mix2_nested_13, p3mix3_nested_13,
          title = "Multi-level Mixed Effects Models (13-Class) - Geographic Levels",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)


tab_model(p3mix_city_13, p3mix_prov_city_13, p3mix_nested_13,
          title = "Multi-level Mixed Effects Models (13-Class) - Geographic Levels",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p3mix_multi_industry_13, p3mix_nested_industry_13, p3mix_complex_13,
          title = "Multi-level Mixed Effects Models (13-Class) - Including Industry",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

## 包含行业固定效应的混合效应模型

```{r lmer_fixed_industry_13class}
# 包含行业固定效应的混合效应模型
p3mix_fixed_industry_13 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log + as.factor(EndYear) + as.factor(industry13) + (1 | PROVINCE),
                                data = dta1)

p3mix_fixed_interaction_13 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                                   ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                                   as.factor(industry13) + (1 | PROVINCE),
                                   data = dta1)

p4mix_fixed_central_13 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               as.factor(industry13) + (1 | PROVINCE),
                               data = dta1)

p4mix_fixed_local_13 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             as.factor(industry13) + (1 | PROVINCE),
                             data = dta1)

# 输出结果
tab_model(p3mix_fixed_industry_13, p3mix_fixed_interaction_13,
          title = "Mixed Effects Models with Industry Fixed Effects (13-Class) - P3 Models",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix_fixed_central_13, p4mix_fixed_local_13,
          title = "Mixed Effects Models with Industry Fixed Effects (13-Class) - P4 Models",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

# 模型比较与分析

## 模型拟合度比较

```{r model_comparison_13class}
# 创建模型比较表
plm_models_13 <- list(p3way1_13, p3way2_13, p3way3_13, p4m1_13, p4m2_13, p4m3_13, p4m4_13)
lmer_models_13 <- list(p3mix1_13, p3mix2_13, p3mix3_13, p4mix1_13, p4mix2_13, p4mix3_13, p4mix4_13)
slope_models_13 <- list(p3mix_slope_13, p4mix_central_slope_13, p4mix_local_slope_13)

model_comparison_13 <- data.frame(
  Model = c("P3 Model 1", "P3 Model 2", "P3 Model 3",
            "P4 Central Main", "P4 Central Interaction", "P4 Local Main", "P4 Local Interaction"),

  # PLM模型
  PLM_R2 = sapply(plm_models_13, function(x) round(summary(x)$r.squared[1], 4)),
  PLM_Obs = sapply(plm_models_13, nobs),

  # LMER模型
  LMER_R2_marginal = sapply(lmer_models_13, function(x) round(performance::r2(x)$R2_marginal, 4)),
  LMER_R2_conditional = sapply(lmer_models_13, function(x) round(performance::r2(x)$R2_conditional, 4)),
  LMER_Obs = sapply(lmer_models_13, nobs)
)

# 显示比较表
kable(model_comparison_13,
      col.names = c("模型", "PLM R²", "PLM 观测数", "LMER R²(边际)", "LMER R²(条件)", "LMER 观测数"),
      caption = "13类行业分类：PLM vs LMER模型比较") %>%
  kable_styling(bootstrap_options = c("striped", "hover", "condensed"),
                full_width = FALSE)

# 计算平均改进
cat("\n=== 模型性能分析 ===\n")
cat("平均PLM R²:", round(mean(model_comparison_13$PLM_R2), 4), "\n")
cat("平均LMER R²(边际):", round(mean(model_comparison_13$LMER_R2_marginal), 4), "\n")
cat("平均LMER R²(条件):", round(mean(model_comparison_13$LMER_R2_conditional), 4), "\n")

# 随机斜率模型性能
slope_r2_marginal_13 <- sapply(slope_models_13, function(x) round(performance::r2(x)$R2_marginal, 4))
slope_r2_conditional_13 <- sapply(slope_models_13, function(x) round(performance::r2(x)$R2_conditional, 4))

cat("\n=== 随机斜率模型性能 ===\n")
cat("P3 随机斜率模型 R²(边际):", slope_r2_marginal_13[1], "\n")
cat("P3 随机斜率模型 R²(条件):", slope_r2_conditional_13[1], "\n")
cat("P4 中央随机斜率模型 R²(边际):", slope_r2_marginal_13[2], "\n")
cat("P4 中央随机斜率模型 R²(条件):", slope_r2_conditional_13[2], "\n")
cat("P4 地方随机斜率模型 R²(边际):", slope_r2_marginal_13[3], "\n")
cat("P4 地方随机斜率模型 R²(条件):", slope_r2_conditional_13[3], "\n")

# 新增模型性能
multi_models_13 <- list(p3mix_multi_industry_13, p3mix_nested_industry_13, p3mix_complex_13)
fixed_models_13 <- list(p3mix_fixed_industry_13, p3mix_fixed_interaction_13, p4mix_fixed_central_13, p4mix_fixed_local_13)

multi_r2_marginal_13 <- sapply(multi_models_13, function(x) round(performance::r2(x)$R2_marginal, 4))
multi_r2_conditional_13 <- sapply(multi_models_13, function(x) round(performance::r2(x)$R2_conditional, 4))
fixed_r2_marginal_13 <- sapply(fixed_models_13, function(x) round(performance::r2(x)$R2_marginal, 4))
fixed_r2_conditional_13 <- sapply(fixed_models_13, function(x) round(performance::r2(x)$R2_conditional, 4))

cat("\n=== 多层级随机效应模型（包含行业）性能 ===\n")
cat("多层级+行业模型 R²(边际):", multi_r2_marginal_13[1], "\n")
cat("多层级+行业模型 R²(条件):", multi_r2_conditional_13[1], "\n")
cat("嵌套+行业模型 R²(边际):", multi_r2_marginal_13[2], "\n")
cat("嵌套+行业模型 R²(条件):", multi_r2_conditional_13[2], "\n")
cat("复杂+行业模型 R²(边际):", multi_r2_marginal_13[3], "\n")
cat("复杂+行业模型 R²(条件):", multi_r2_conditional_13[3], "\n")

cat("\n=== 行业固定效应混合模型性能 ===\n")
cat("P3 基础固定效应模型 R²(边际):", fixed_r2_marginal_13[1], "\n")
cat("P3 基础固定效应模型 R²(条件):", fixed_r2_conditional_13[1], "\n")
cat("P3 交互固定效应模型 R²(边际):", fixed_r2_marginal_13[2], "\n")
cat("P3 交互固定效应模型 R²(条件):", fixed_r2_conditional_13[2], "\n")
cat("P4 中央固定效应模型 R²(边际):", fixed_r2_marginal_13[3], "\n")
cat("P4 中央固定效应模型 R²(条件):", fixed_r2_conditional_13[3], "\n")
cat("P4 地方固定效应模型 R²(边际):", fixed_r2_marginal_13[4], "\n")
cat("P4 地方固定效应模型 R²(条件):", fixed_r2_conditional_13[4], "\n")
```

## 行业分类效果分析

```{r industry_effects_13class}
# 分析13类行业分类的分布
industry_summary_13 <- dta1 %>%
  group_by(industry13) %>%
  summarise(
    company_count = n_distinct(Symbol),
    observation_count = n(),
    mean_disclosure = round(mean(Environmental_Information_Disclosure, na.rm = TRUE), 3),
    .groups = 'drop'
  ) %>%
  arrange(desc(company_count))

kable(industry_summary_13,
      col.names = c("行业分类", "公司数量", "观测数量", "平均披露水平"),
      caption = "13类行业分类详细统计") %>%
  kable_styling(bootstrap_options = c("striped", "hover", "condensed"),
                full_width = FALSE)
```

## 随机效应分析

```{r random_effects_analysis_13class}
# 分析不同随机效应结构的ICC
icc_province <- performance::icc(p3mix1_13)
icc_industry <- performance::icc(p3mix4_13)
icc_complex <- performance::icc(p3mix_complex_13)

cat("=== 组内相关系数 (ICC) 分析 ===\n")
cat("省份随机效应模型 ICC:", round(icc_province$ICC_adjusted, 4), "\n")
cat("省份+行业随机效应模型 ICC:", round(icc_industry$ICC_adjusted, 4), "\n")
cat("复杂随机效应模型 ICC:", round(icc_complex$ICC_adjusted, 4), "\n")

# 模型比较 - AIC/BIC
model_fit_comparison <- data.frame(
  Model = c("Province Only", "Province + Industry", "Complex Model"),
  AIC = c(AIC(p3mix1_13), AIC(p3mix4_13), AIC(p3mix_complex_13)),
  BIC = c(BIC(p3mix1_13), BIC(p3mix4_13), BIC(p3mix_complex_13)),
  ICC = c(icc_province$ICC_adjusted, icc_industry$ICC_adjusted, icc_complex$ICC_adjusted)
)

kable(model_fit_comparison,
      col.names = c("模型", "AIC", "BIC", "ICC"),
      caption = "混合效应模型拟合度比较") %>%
  kable_styling(bootstrap_options = c("striped", "hover", "condensed"),
                full_width = FALSE)
```