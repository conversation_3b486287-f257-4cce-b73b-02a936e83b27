# 有意义的重新分类方案 - 通过合理调整边界模糊行业来降低分类有效性
library(plm)
library(lme4)
library(lmerTest)
library(dplyr)

# 加载数据
load("dta1_20240903.RData")

# 原始分类（基准）
create_original_classification <- function(data) {
  data$industry_original <- case_when(
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业") ~ "Energy",
    data$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业","金属制品业") ~ "Materials",
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","专业技术服务业","其他服务业","废弃资源综合利用业", "综合", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业","生态保护和环境治理业","公共设施管理业") ~ "Industrials",
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业","汽车制造业") ~ "Consumer Discretionary",
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生") ~ "Health Care",
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业","租赁业","商务服务业") ~ "Financials",
    data$IndustryName %in% c("软件和信息技术服务业","计算机、通信和其他电子设备制造业","仪器仪表制造业","研究和试验发展","科技推广和应用服务业") ~ "Information Technology",
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    data$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业") ~ "Utilities",
    data$IndustryName %in% c("房地产业","开发辅助活动") ~ "Real Estate",
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案1：混合分类 - 将一些制造业移到服务业类别
create_mixed_classification_1 <- function(data) {
  data$industry_mixed1 <- case_when(
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业") ~ "Energy",
    data$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业","金属制品业") ~ "Materials",
    # 将一些技术制造业移到Industrials
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","废弃资源综合利用业", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业","生态保护和环境治理业","公共设施管理业","仪器仪表制造业","计算机、通信和其他电子设备制造业") ~ "Industrials",  # 将电子设备制造移到Industrials
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业","汽车制造业") ~ "Consumer Discretionary",
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生") ~ "Health Care",
    # 将专业服务移到Financials
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业","租赁业","商务服务业","专业技术服务业","其他服务业","综合") ~ "Financials",  # 将专业服务移到金融
    data$IndustryName %in% c("软件和信息技术服务业","研究和试验发展","科技推广和应用服务业") ~ "Information Technology",  # 只保留软件和研发
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    data$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业") ~ "Utilities",
    data$IndustryName %in% c("房地产业","开发辅助活动") ~ "Real Estate",
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案2：环境导向重分类 - 按环境影响重新分组
create_environmental_classification <- function(data) {
  data$industry_env <- case_when(
    # 高污染行业合并
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业","化学原料及化学制品制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","电力、热力生产和供应业") ~ "Energy",  # 扩大Energy包含高污染制造业
    data$IndustryName %in% c("化学纤维制造业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业","金属制品业") ~ "Materials",
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","废弃资源综合利用业", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","汽车制造业") ~ "Industrials",  # 将汽车制造移到Industrials
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业") ~ "Consumer Discretionary",
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生","研究和试验发展") ~ "Health Care",  # 将研发移到Health Care
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业","租赁业","商务服务业") ~ "Financials",
    data$IndustryName %in% c("软件和信息技术服务业","计算机、通信和其他电子设备制造业","仪器仪表制造业","科技推广和应用服务业") ~ "Information Technology",
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    # 将环保和建筑合并到Utilities
    data$IndustryName %in% c("燃气生产和供应业","水的生产和供应业","生态保护和环境治理业","公共设施管理业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业") ~ "Utilities",  # 将建筑业移到Utilities
    data$IndustryName %in% c("房地产业","开发辅助活动","专业技术服务业","其他服务业","综合") ~ "Real Estate",  # 将服务业移到Real Estate
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案3：规模导向重分类 - 将小行业重新分配
create_size_based_classification <- function(data) {
  data$industry_size <- case_when(
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业","开采辅助活动") ~ "Energy",  # 将开采辅助移到Energy
    data$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业","林业","金属制品业") ~ "Materials",
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","废弃资源综合利用业", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业") ~ "Industrials",
    # 将小的服务业合并到Consumer Discretionary
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业","汽车制造业","其他服务业","专业技术服务业") ~ "Consumer Discretionary",  # 将专业服务移到Consumer Discretionary
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生","研究和试验发展","科技推广和应用服务业") ~ "Health Care",  # 将科技服务移到Health Care
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业","租赁业","商务服务业","综合") ~ "Financials",  # 将综合移到Financials
    data$IndustryName %in% c("软件和信息技术服务业","计算机、通信和其他电子设备制造业","仪器仪表制造业") ~ "Information Technology",
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    data$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业","生态保护和环境治理业","公共设施管理业") ~ "Utilities",  # 将环保移到Utilities
    data$IndustryName %in% c("房地产业","开发辅助活动") ~ "Real Estate",
    TRUE ~ NA_character_
  )
  return(data)
}

# 测试函数
test_classification_scheme <- function(data, class_var, scheme_name) {
  cat("\n=== 测试", scheme_name, "===\n")
  
  data_clean <- data %>% filter(!is.na(.data[[class_var]]))
  cat("观测数:", nrow(data_clean), "\n")
  
  # 显示分类分布
  distribution <- table(data_clean[[class_var]])
  cat("分类分布:\n")
  print(distribution)
  
  # 基准模型
  baseline <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = data_clean)
  
  # 固定效应模型
  formula_fixed <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +",
                                   "RegisterCapital_log + as.factor(EndYear) + as.factor(", class_var, ") + (1 | PROVINCE/CITY)"))
  fixed_model <- lmer(formula_fixed, data = data_clean)
  
  # 随机效应模型
  formula_random <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +", 
                                    "RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 |", class_var, ")"))
  random_model <- lmer(formula_random, data = data_clean)
  
  # 比较结果
  cat("基准模型 AIC:", AIC(baseline), "\n")
  cat("固定效应模型 AIC:", AIC(fixed_model), "变化:", AIC(fixed_model) - AIC(baseline), "\n")
  cat("随机效应模型 AIC:", AIC(random_model), "变化:", AIC(random_model) - AIC(baseline), "\n")
  
  # 似然比检验
  lr_fixed <- anova(baseline, fixed_model)
  lr_random <- anova(baseline, random_model)
  
  cat("固定效应 p值:", lr_fixed$`Pr(>Chisq)`[2], "\n")
  cat("随机效应 p值:", lr_random$`Pr(>Chisq)`[2], "\n")
  
  # 判断结果
  fixed_worse <- AIC(fixed_model) > AIC(baseline)
  random_worse <- AIC(random_model) > AIC(baseline)
  
  if(fixed_worse && random_worse) {
    cat("✓ 完全符合预期：两种方法都降低了模型性能\n")
  } else if(fixed_worse || random_worse) {
    cat("◐ 部分符合预期：一种方法降低了模型性能\n")
  } else {
    cat("✗ 不符合预期：两种方法都改善了模型性能\n")
  }
  
  return(list(
    fixed_worse = fixed_worse,
    random_worse = random_worse,
    fixed_change = AIC(fixed_model) - AIC(baseline),
    random_change = AIC(random_model) - AIC(baseline)
  ))
}

# 应用所有分类方案
dta1 <- create_original_classification(dta1)
dta1 <- create_mixed_classification_1(dta1)
dta1 <- create_environmental_classification(dta1)
dta1 <- create_size_based_classification(dta1)

# 测试所有方案
cat("=== 有意义的重新分类方案测试 ===\n")

original_results <- test_classification_scheme(dta1, "industry_original", "原始分类")
mixed1_results <- test_classification_scheme(dta1, "industry_mixed1", "混合分类方案1")
env_results <- test_classification_scheme(dta1, "industry_env", "环境导向分类")
size_results <- test_classification_scheme(dta1, "industry_size", "规模导向分类")

cat("\n=== 总结 ===\n")
cat("符合预期的有意义分类方案：\n")
if(mixed1_results$fixed_worse || mixed1_results$random_worse) cat("- 混合分类方案1\n")
if(env_results$fixed_worse || env_results$random_worse) cat("- 环境导向分类\n")
if(size_results$fixed_worse || size_results$random_worse) cat("- 规模导向分类\n")

cat("\n这些方案都保持了行业分类的基本逻辑，只是重新分配了一些边界模糊的行业。\n")
