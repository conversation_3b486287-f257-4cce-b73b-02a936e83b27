---
title: "11类行业分类回归分析"
author: "ESG China Research"
date: "`r Sys.Date()`"
format: 
  html:
    toc: true
    toc-depth: 3
    code-fold: false
    theme: cosmo
editor: visual
---

# 数据准备

## 加载必要的库和数据

```{r setup, message=FALSE, warning=FALSE}
# 加载必要的库
library(plm)
library(lme4)
library(lmerTest)
library(stargazer)
library(sjPlot)
library(dplyr)
library(knitr)
library(kableExtra)
library(stringr)

# 加载数据
load("dta1_20240903.RData")

# 检查数据结构
cat("原始数据概览:\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")
```

## 创建11类行业分类

```{r industry_classification_11}
# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # 1 Energy
  dta1$IndustryName %in% c("石油和天然气开采业",
                           "石油加工、炼焦及核燃料加工业", "开发辅助活动",
                           "煤炭开采和洗选业")                                   ~ "Energy",
  
  # 2 Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                           "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                           "木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业",
                           "金属制品业")                                         ~ "Materials",
  
  # 3 Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业",
                           "电气机械及器材制造业","通用设备制造业",
                           "装卸搬运和运输代理业","道路运输业","水上运输业",
                           "航空运输业","铁路运输业","仓储业","其他制造业",
                           "专业技术服务业","其他服务业",
                           "废弃资源综合利用业", "综合", "金属制品、机械和设备修理业",
                           "邮政业","印刷和记录媒介复制业",
                           # 建筑／环保
                           "房屋建筑业","建筑安装业","土木工程建筑业",
                           "建筑装饰和其他建筑业","生态保护和环境治理业",
                           "公共设施管理业")                                     ~ "Industrials",
  
  # 4 Consumer Discretionary
  dta1$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业",
                           "教育","纺织业","零售业","纺织服装、服饰业",
                           "文教、工美、体育和娱乐用品制造业","文化艺术业",
                           "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业",
                           "机动车、电子产品和日用产品修理业",
                           "汽车制造业")                                         ~ "Consumer Discretionary",
  
  # 5 Consumer Staples
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业",
                           "农业","畜牧业","渔业",
                           "农、林、牧、渔服务业")                               ~ "Consumer Staples",
  
  # 6 Health Care
  dta1$IndustryName %in% c("医药制造业","卫生")                                 ~ "Health Care",
  
  # 7 Financials
  dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业",
                           "其他金融业","租赁业","商务服务业")                   ~ "Financials",
  
  # 8 Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业",
                           "计算机、通信和其他电子设备制造业",
                           "仪器仪表制造业",
                           "研究和试验发展","科技推广和应用服务业")               ~ "Information Technology",
  
  # 9 Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务",
                           "广播、电视、电影和影视录音制作业",
                           "新闻和出版业","互联网和相关服务")                     ~ "Communication Services",
  
  # 10 Utilities
  dta1$IndustryName %in% c("电力、热力生产和供应业",
                           "燃气生产和供应业",
                           "水的生产和供应业")                                   ~ "Utilities",
  
  # 11 Real Estate
  dta1$IndustryName %in% c("房地产业")                            ~ "Real Estate",
  
  TRUE ~ NA_character_
)


# 显示分类结果
cat("11类分类结果:\n")
industry_table <- table(dta1$industry_type11, useNA = "always")
print(industry_table)

# 分析NA值的详细情况
na_data <- dta1 %>% filter(is.na(industry_type11))
cat("\n=== NA值分析 ===\n")
cat("NA观测数:", nrow(na_data), "\n")
cat("NA占总观测数的比例:", round(nrow(na_data)/nrow(dta1)*100, 2), "%\n")
cat("NA涉及的唯一公司数:", length(unique(na_data$Symbol)), "\n")
cat("NA涉及的唯一行业数:", length(unique(na_data$IndustryName)), "\n")

# 显示所有未分类的行业名称
cat("\n未分类的行业名称:\n")
na_industries <- na_data %>%
  group_by(IndustryName) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(desc(count))
print(na_industries)

# 创建去除NA的数据集用于分析
dta1_11class <- dta1 %>% filter(!is.na(industry_type11))
cat("\n去除NA后的观测数:", nrow(dta1_11class), "\n")
cat("去除NA后的公司数:", length(unique(dta1_11class$Symbol)), "\n")
```

## NA值进一步分类方案

```{r na_classification_analysis}
# 对NA值进行进一步分类
# 基于行业名称的关键词匹配进行补充分类

# 创建补充分类函数
classify_na_industries <- function(industry_name) {
  case_when(
    # 检查是否包含特定关键词
    str_detect(industry_name, "金融|银行|证券|保险|投资|基金|信托|担保|租赁") ~ "Financials",
    str_detect(industry_name, "房地产|建筑|装修|装饰|土建|工程") ~ "Real Estate",
    str_detect(industry_name, "制造|生产|加工|机械|设备|器材|仪器") ~ "Industrials",
    str_detect(industry_name, "化工|化学|材料|金属|矿物|钢铁|有色|塑料|橡胶") ~ "Materials",
    str_detect(industry_name, "电力|能源|燃气|石油|煤炭|新能源") ~ "Energy",
    str_detect(industry_name, "医药|医疗|卫生|健康|生物|制药") ~ "Health Care",
    str_detect(industry_name, "食品|农业|畜牧|渔业|林业|饮料") ~ "Consumer Staples",
    str_detect(industry_name, "零售|批发|商贸|消费|服装|纺织|家具|娱乐|旅游|酒店|餐饮") ~ "Consumer Discretionary",
    str_detect(industry_name, "软件|信息|技术|计算机|电子|通信|互联网|科技") ~ "Information Technology",
    str_detect(industry_name, "电信|广播|媒体|出版|传媒") ~ "Communication Services",
    str_detect(industry_name, "水务|环保|公用|供水|供气|垃圾|污水") ~ "Utilities",
    TRUE ~ "Other"  # 仍然无法分类的归为Other
  )
}

# 应用补充分类
na_data_classified <- na_data %>%
  mutate(
    suggested_classification = classify_na_industries(IndustryName),
    classification_confidence = case_when(
      suggested_classification == "Other" ~ "Low",
      TRUE ~ "Medium"
    )
  )

# 显示补充分类结果
cat("=== 补充分类结果 ===\n")
classification_summary <- na_data_classified %>%
  group_by(suggested_classification) %>%
  summarise(
    count = n(),
    unique_companies = length(unique(Symbol)),
    unique_industries = length(unique(IndustryName)),
    .groups = 'drop'
  ) %>%
  arrange(desc(count))

print(classification_summary)

# 显示每个补充分类中的具体行业
cat("\n=== 各分类中的具体行业 ===\n")
for(class in unique(classification_summary$suggested_classification)) {
  cat("\n", class, ":\n")
  industries_in_class <- na_data_classified %>%
    filter(suggested_classification == class) %>%
    group_by(IndustryName) %>%
    summarise(count = n(), .groups = 'drop') %>%
    arrange(desc(count))
  print(industries_in_class)
}
```

## 创建完整的12类分类（包含Other类）

```{r create_12class_classification}
# 创建包含补充分类的完整数据集
dta1_12class <- dta1 %>%
  mutate(
    industry_type12 = case_when(
      !is.na(industry_type11) ~ industry_type11,
      is.na(industry_type11) ~ classify_na_industries(IndustryName)
    )
  )

# 显示12类分类结果
cat("=== 12类分类结果（包含Other） ===\n")
industry_table_12 <- table(dta1_12class$industry_type12, useNA = "always")
print(industry_table_12)

# 计算分类覆盖率
coverage_stats <- dta1_12class %>%
  summarise(
    total_obs = n(),
    classified_obs = sum(!is.na(industry_type12) & industry_type12 != "Other"),
    other_obs = sum(industry_type12 == "Other", na.rm = TRUE),
    still_na = sum(is.na(industry_type12)),
    coverage_rate = round(classified_obs / total_obs * 100, 2),
    other_rate = round(other_obs / total_obs * 100, 2)
  )

cat("\n=== 分类覆盖率统计 ===\n")
cat("总观测数:", coverage_stats$total_obs, "\n")
cat("已分类观测数:", coverage_stats$classified_obs, "\n")
cat("Other类观测数:", coverage_stats$other_obs, "\n")
cat("仍为NA的观测数:", coverage_stats$still_na, "\n")
cat("分类覆盖率:", coverage_stats$coverage_rate, "%\n")
cat("Other类占比:", coverage_stats$other_rate, "%\n")

# 保存12类分类数据用于后续分析
dta1_12class_clean <- dta1_12class %>% filter(industry_type12 != "Other" & !is.na(industry_type12))
cat("\n最终用于分析的观测数:", nrow(dta1_12class_clean), "\n")
cat("最终用于分析的公司数:", length(unique(dta1_12class_clean$Symbol)), "\n")
```

# 模型比较分析：添加vs不添加industry_type11

## 第一步：基准模型（不包含行业分类）

```{r baseline_models_without_industry}
# ===== 基准模型组：不包含任何行业效应 =====
# 这些模型只包含省份/城市随机效应，作为比较的基准

# P3系列基准模型
baseline_p3_1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                     data = dta1_11class)

baseline_p3_2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                     data = dta1_11class)

baseline_p3_3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                     data = dta1_11class)

# P4系列基准模型 - 中央政治关联
baseline_p4_central_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                             RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                             data = dta1_11class)

baseline_p4_central_2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             (1 | PROVINCE/CITY),
                             data = dta1_11class)

# P4系列基准模型 - 地方政治关联
baseline_p4_local_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                           data = dta1_11class)

baseline_p4_local_2 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                           (1 | PROVINCE/CITY),
                           data = dta1_11class)

cat("=== 基准模型创建完成 ===\n")
cat("已创建7个基准模型（不包含行业效应）\n")
```

## 第二步：包含行业固定效应的对比模型

```{r industry_fixed_effects_models}
# ===== 行业固定效应模型组 =====
# 这些模型在基准模型基础上添加industry_type11作为固定效应

# P3系列 + 行业固定效应
fixed_p3_1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE/CITY),
                   data = dta1_11class)

fixed_p3_2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE/CITY),
                   data = dta1_11class)

fixed_p3_3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                   ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                   as.factor(industry_type11) + (1 | PROVINCE/CITY),
                   data = dta1_11class)

# P4系列 + 行业固定效应 - 中央政治关联
fixed_p4_central_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection +
                          ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                          as.factor(industry_type11) + (1 | PROVINCE/CITY),
                          data = dta1_11class)

fixed_p4_central_2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                          ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                          as.factor(industry_type11) + (1 | PROVINCE/CITY),
                          data = dta1_11class)

# P4系列 + 行业固定效应 - 地方政治关联
fixed_p4_local_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection +
                        ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                        as.factor(industry_type11) + (1 | PROVINCE/CITY),
                        data = dta1_11class)

fixed_p4_local_2 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                        ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                        as.factor(industry_type11) + (1 | PROVINCE/CITY),
                        data = dta1_11class)

cat("=== 行业固定效应模型创建完成 ===\n")
cat("已创建7个包含行业固定效应的模型\n")
```

## 第三步：包含行业随机效应的对比模型

```{r industry_random_effects_models}
# ===== 行业随机效应模型组 =====
# 这些模型在基准模型基础上添加industry_type11作为随机效应

# P3系列 + 行业随机效应
random_p3_1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 | industry_type11),
                   data = dta1_11class)

random_p3_2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 | industry_type11),
                   data = dta1_11class)

random_p3_3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                   ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                   (1 | PROVINCE/CITY) + (1 | industry_type11),
                   data = dta1_11class)

# P4系列 + 行业随机效应 - 中央政治关联
random_p4_central_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 | industry_type11),
                           data = dta1_11class)

random_p4_central_2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                           (1 | PROVINCE/CITY) + (1 | industry_type11),
                           data = dta1_11class)

# P4系列 + 行业随机效应 - 地方政治关联
random_p4_local_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                         RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 | industry_type11),
                         data = dta1_11class)

random_p4_local_2 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                         ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                         (1 | PROVINCE/CITY) + (1 | industry_type11),
                         data = dta1_11class)

cat("=== 行业随机效应模型创建完成 ===\n")
cat("已创建7个包含行业随机效应的模型\n")
```

## 第四步：模型性能指标计算

```{r model_performance_metrics}
# ===== 计算所有模型的性能指标 =====
library(performance)
library(MuMIn)

# 创建模型列表
baseline_models <- list(
  "P3_1_baseline" = baseline_p3_1,
  "P3_2_baseline" = baseline_p3_2,
  "P3_3_baseline" = baseline_p3_3,
  "P4_central_1_baseline" = baseline_p4_central_1,
  "P4_central_2_baseline" = baseline_p4_central_2,
  "P4_local_1_baseline" = baseline_p4_local_1,
  "P4_local_2_baseline" = baseline_p4_local_2
)

fixed_models <- list(
  "P3_1_fixed" = fixed_p3_1,
  "P3_2_fixed" = fixed_p3_2,
  "P3_3_fixed" = fixed_p3_3,
  "P4_central_1_fixed" = fixed_p4_central_1,
  "P4_central_2_fixed" = fixed_p4_central_2,
  "P4_local_1_fixed" = fixed_p4_local_1,
  "P4_local_2_fixed" = fixed_p4_local_2
)

random_models <- list(
  "P3_1_random" = random_p3_1,
  "P3_2_random" = random_p3_2,
  "P3_3_random" = random_p3_3,
  "P4_central_1_random" = random_p4_central_1,
  "P4_central_2_random" = random_p4_central_2,
  "P4_local_1_random" = random_p4_local_1,
  "P4_local_2_random" = random_p4_local_2
)

# 计算性能指标的函数
calculate_model_metrics <- function(model_list, model_type) {
  results <- data.frame()

  for(i in 1:length(model_list)) {
    model <- model_list[[i]]
    model_name <- names(model_list)[i]

    # 基本信息
    aic_val <- AIC(model)
    bic_val <- BIC(model)
    loglik_val <- as.numeric(logLik(model))

    # R-squared
    r2_vals <- r.squaredGLMM(model)
    r2_marginal <- r2_vals[1]
    r2_conditional <- r2_vals[2]

    # ICC
    icc_val <- performance::icc(model)$ICC_adjusted

    # 自由度和观测数
    df_val <- attr(logLik(model), "df")
    nobs_val <- nobs(model)

    # 组合结果
    row_result <- data.frame(
      Model = model_name,
      Type = model_type,
      AIC = aic_val,
      BIC = bic_val,
      LogLik = loglik_val,
      R2_marginal = r2_marginal,
      R2_conditional = r2_conditional,
      ICC = icc_val,
      DF = df_val,
      N_obs = nobs_val,
      stringsAsFactors = FALSE
    )

    results <- rbind(results, row_result)
  }

  return(results)
}

# 计算所有模型的指标
baseline_metrics <- calculate_model_metrics(baseline_models, "Baseline")
fixed_metrics <- calculate_model_metrics(fixed_models, "Fixed_Industry")
random_metrics <- calculate_model_metrics(random_models, "Random_Industry")

# 合并所有结果
all_metrics <- rbind(baseline_metrics, fixed_metrics, random_metrics)

# 显示结果
cat("=== 模型性能指标计算完成 ===\n")
print(all_metrics)
```

## 第五步：似然比检验

```{r likelihood_ratio_tests}
# ===== 似然比检验：比较嵌套模型 =====

# 创建比较函数
perform_lr_test <- function(baseline_model, comparison_model, comparison_name) {
  lr_test <- anova(baseline_model, comparison_model)

  result <- data.frame(
    Comparison = comparison_name,
    Chi_square = lr_test$Chisq[2],
    DF_diff = lr_test$Df[2],
    P_value = lr_test$`Pr(>Chisq)`[2],
    Significant = lr_test$`Pr(>Chisq)`[2] < 0.05,
    stringsAsFactors = FALSE
  )

  return(result)
}

# 进行所有的似然比检验
lr_results <- data.frame()

# P3系列检验
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_1, fixed_p3_1, "P3_1: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_1, random_p3_1, "P3_1: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_2, fixed_p3_2, "P3_2: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_2, random_p3_2, "P3_2: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_3, fixed_p3_3, "P3_3: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_3, random_p3_3, "P3_3: Baseline vs Random"))

# P4中央系列检验
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_1, fixed_p4_central_1, "P4_Central_1: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_1, random_p4_central_1, "P4_Central_1: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_2, fixed_p4_central_2, "P4_Central_2: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_2, random_p4_central_2, "P4_Central_2: Baseline vs Random"))

# P4地方系列检验
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_1, fixed_p4_local_1, "P4_Local_1: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_1, random_p4_local_1, "P4_Local_1: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_2, fixed_p4_local_2, "P4_Local_2: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_2, random_p4_local_2, "P4_Local_2: Baseline vs Random"))

# 显示似然比检验结果
cat("=== 似然比检验结果 ===\n")
print(lr_results)

# 统计显著性结果
significant_improvements <- sum(lr_results$Significant)
total_comparisons <- nrow(lr_results)

cat("\n=== 似然比检验总结 ===\n")
cat("总比较次数:", total_comparisons, "\n")
cat("显著改善的模型数:", significant_improvements, "\n")
cat("显著改善比例:", round(significant_improvements/total_comparisons*100, 2), "%\n")
```

## 第六步：创建模型比较汇总表格

```{r model_comparison_tables}
# ===== 创建详细的模型比较表格 =====

# 重新整理数据以便比较
library(dplyr)
library(kableExtra)

# 为每个模型组创建比较表
create_comparison_table <- function(baseline_name, fixed_name, random_name) {
  baseline_row <- all_metrics[all_metrics$Model == baseline_name, ]
  fixed_row <- all_metrics[all_metrics$Model == fixed_name, ]
  random_row <- all_metrics[all_metrics$Model == random_name, ]

  comparison_df <- rbind(baseline_row, fixed_row, random_row)

  # 计算相对于基准模型的变化
  comparison_df$AIC_diff <- comparison_df$AIC - baseline_row$AIC
  comparison_df$BIC_diff <- comparison_df$BIC - baseline_row$BIC
  comparison_df$LogLik_diff <- comparison_df$LogLik - baseline_row$LogLik
  comparison_df$R2_marginal_diff <- comparison_df$R2_marginal - baseline_row$R2_marginal
  comparison_df$R2_conditional_diff <- comparison_df$R2_conditional - baseline_row$R2_conditional

  return(comparison_df)
}

# P3系列比较
cat("=== P3系列模型比较 ===\n")

p3_1_comparison <- create_comparison_table("P3_1_baseline", "P3_1_fixed", "P3_1_random")
p3_2_comparison <- create_comparison_table("P3_2_baseline", "P3_2_fixed", "P3_2_random")
p3_3_comparison <- create_comparison_table("P3_3_baseline", "P3_3_fixed", "P3_3_random")

print("P3_1 模型比较:")
print(p3_1_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P3_2 模型比较:")
print(p3_2_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P3_3 模型比较:")
print(p3_3_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

# P4系列比较
cat("\n=== P4系列模型比较 ===\n")

p4_central_1_comparison <- create_comparison_table("P4_central_1_baseline", "P4_central_1_fixed", "P4_central_1_random")
p4_central_2_comparison <- create_comparison_table("P4_central_2_baseline", "P4_central_2_fixed", "P4_central_2_random")
p4_local_1_comparison <- create_comparison_table("P4_local_1_baseline", "P4_local_1_fixed", "P4_local_1_random")
p4_local_2_comparison <- create_comparison_table("P4_local_2_baseline", "P4_local_2_fixed", "P4_local_2_random")

print("P4_Central_1 模型比较:")
print(p4_central_1_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P4_Central_2 模型比较:")
print(p4_central_2_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P4_Local_1 模型比较:")
print(p4_local_1_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P4_Local_2 模型比较:")
print(p4_local_2_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])
```

## 第七步：结果分析和可视化

```{r results_analysis_visualization}
# ===== 结果分析和可视化 =====
library(ggplot2)
library(gridExtra)

# 1. AIC/BIC比较图
aic_bic_plot <- all_metrics %>%
  select(Model, Type, AIC, BIC) %>%
  tidyr::pivot_longer(cols = c(AIC, BIC), names_to = "Metric", values_to = "Value") %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  ggplot(aes(x = Model_Group, y = Value, fill = Type)) +
  geom_bar(stat = "identity", position = "dodge") +
  facet_wrap(~Metric, scales = "free_y") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "AIC和BIC比较",
       subtitle = "较低的值表示更好的模型拟合",
       x = "模型组", y = "信息准则值") +
  scale_fill_manual(values = c("Baseline" = "#E74C3C",
                              "Fixed_Industry" = "#3498DB",
                              "Random_Industry" = "#2ECC71"))

print(aic_bic_plot)

# 2. R²比较图
r2_plot <- all_metrics %>%
  select(Model, Type, R2_marginal, R2_conditional) %>%
  tidyr::pivot_longer(cols = c(R2_marginal, R2_conditional), names_to = "R2_Type", values_to = "Value") %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  ggplot(aes(x = Model_Group, y = Value, fill = Type)) +
  geom_bar(stat = "identity", position = "dodge") +
  facet_wrap(~R2_Type, scales = "free_y") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "R²比较",
       subtitle = "较高的值表示更好的解释能力",
       x = "模型组", y = "R²值") +
  scale_fill_manual(values = c("Baseline" = "#E74C3C",
                              "Fixed_Industry" = "#3498DB",
                              "Random_Industry" = "#2ECC71"))

print(r2_plot)

# 3. ICC比较图
icc_plot <- all_metrics %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  ggplot(aes(x = Model_Group, y = ICC, fill = Type)) +
  geom_bar(stat = "identity", position = "dodge") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "ICC (组内相关系数) 比较",
       subtitle = "显示随机效应的重要性",
       x = "模型组", y = "ICC值") +
  scale_fill_manual(values = c("Baseline" = "#E74C3C",
                              "Fixed_Industry" = "#3498DB",
                              "Random_Industry" = "#2ECC71"))

print(icc_plot)

# 4. 创建性能改善/恶化统计
performance_summary <- all_metrics %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  group_by(Model_Group) %>%
  summarise(
    Baseline_AIC = AIC[Type == "Baseline"],
    Fixed_AIC = AIC[Type == "Fixed_Industry"],
    Random_AIC = AIC[Type == "Random_Industry"],
    Fixed_AIC_Change = Fixed_AIC - Baseline_AIC,
    Random_AIC_Change = Random_AIC - Baseline_AIC,
    Fixed_Worse = Fixed_AIC_Change > 0,
    Random_Worse = Random_AIC_Change > 0,
    .groups = 'drop'
  )

cat("\n=== 性能变化总结 ===\n")
print(performance_summary)

# 统计有多少模型在添加行业分类后性能变差
fixed_worse_count <- sum(performance_summary$Fixed_Worse)
random_worse_count <- sum(performance_summary$Random_Worse)
total_models <- nrow(performance_summary)

cat("\n=== 最终结果统计 ===\n")
cat("总模型组数:", total_models, "\n")
cat("添加固定行业效应后AIC变差的模型数:", fixed_worse_count, "\n")
cat("添加随机行业效应后AIC变差的模型数:", random_worse_count, "\n")
cat("固定效应恶化比例:", round(fixed_worse_count/total_models*100, 2), "%\n")
cat("随机效应恶化比例:", round(random_worse_count/total_models*100, 2), "%\n")
```

## 第八步：详细结论分析

```{r detailed_conclusions}
# ===== 详细结论分析 =====

cat("=== 模型比较分析结论 ===\n\n")

# 1. 整体性能比较
cat("1. 整体性能比较:\n")
cat("   - 我们比较了21个模型（7个基准模型 + 7个固定效应模型 + 7个随机效应模型）\n")
cat("   - 基准模型：只包含省份/城市随机效应，不包含行业分类\n")
cat("   - 固定效应模型：在基准模型基础上添加industry_type11作为固定效应\n")
cat("   - 随机效应模型：在基准模型基础上添加industry_type11作为随机效应\n\n")

# 2. AIC/BIC分析
aic_improvements_fixed <- sum(performance_summary$Fixed_AIC_Change < 0)
aic_improvements_random <- sum(performance_summary$Random_AIC_Change < 0)

cat("2. AIC/BIC信息准则分析:\n")
cat("   - AIC/BIC越低表示模型拟合越好\n")
cat("   - 固定效应模型中AIC改善的模型数:", aic_improvements_fixed, "/", total_models, "\n")
cat("   - 随机效应模型中AIC改善的模型数:", aic_improvements_random, "/", total_models, "\n")
cat("   - 固定效应改善比例:", round(aic_improvements_fixed/total_models*100, 2), "%\n")
cat("   - 随机效应改善比例:", round(aic_improvements_random/total_models*100, 2), "%\n\n")

# 3. R²分析
r2_analysis <- all_metrics %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  group_by(Model_Group) %>%
  summarise(
    Baseline_R2_marginal = R2_marginal[Type == "Baseline"],
    Fixed_R2_marginal = R2_marginal[Type == "Fixed_Industry"],
    Random_R2_marginal = R2_marginal[Type == "Random_Industry"],
    Fixed_R2_improvement = Fixed_R2_marginal - Baseline_R2_marginal,
    Random_R2_improvement = Random_R2_marginal - Baseline_R2_marginal,
    .groups = 'drop'
  )

r2_fixed_improvements <- sum(r2_analysis$Fixed_R2_improvement > 0)
r2_random_improvements <- sum(r2_analysis$Random_R2_improvement > 0)

cat("3. R²解释能力分析:\n")
cat("   - R²越高表示模型解释能力越强\n")
cat("   - 固定效应模型中边际R²改善的模型数:", r2_fixed_improvements, "/", total_models, "\n")
cat("   - 随机效应模型中边际R²改善的模型数:", r2_random_improvements, "/", total_models, "\n")
cat("   - 固定效应R²改善比例:", round(r2_fixed_improvements/total_models*100, 2), "%\n")
cat("   - 随机效应R²改善比例:", round(r2_random_improvements/total_models*100, 2), "%\n\n")

# 4. 似然比检验分析
significant_fixed <- sum(grepl("Fixed", lr_results$Comparison) & lr_results$Significant)
significant_random <- sum(grepl("Random", lr_results$Comparison) & lr_results$Significant)
total_fixed_tests <- sum(grepl("Fixed", lr_results$Comparison))
total_random_tests <- sum(grepl("Random", lr_results$Comparison))

cat("4. 似然比检验分析:\n")
cat("   - 检验添加行业分类是否显著改善模型拟合\n")
cat("   - 固定效应显著改善的模型数:", significant_fixed, "/", total_fixed_tests, "\n")
cat("   - 随机效应显著改善的模型数:", significant_random, "/", total_random_tests, "\n")
cat("   - 固定效应显著改善比例:", round(significant_fixed/total_fixed_tests*100, 2), "%\n")
cat("   - 随机效应显著改善比例:", round(significant_random/total_random_tests*100, 2), "%\n\n")

# 5. 最终结论
cat("5. 最终结论:\n")

if(fixed_worse_count > total_models/2) {
  cat("   ✓ 添加industry_type11作为固定效应在大多数情况下降低了模型性能\n")
} else {
  cat("   ✗ 添加industry_type11作为固定效应在大多数情况下改善了模型性能\n")
}

if(random_worse_count > total_models/2) {
  cat("   ✓ 添加industry_type11作为随机效应在大多数情况下降低了模型性能\n")
} else {
  cat("   ✗ 添加industry_type11作为随机效应在大多数情况下改善了模型性能\n")
}

if(aic_improvements_fixed < total_models/2 && aic_improvements_random < total_models/2) {
  cat("   ✓ 根据AIC准则，添加行业分类总体上降低了模型拟合质量\n")
} else {
  cat("   ✗ 根据AIC准则，添加行业分类总体上改善了模型拟合质量\n")
}

if(significant_fixed < total_fixed_tests/2 && significant_random < total_random_tests/2) {
  cat("   ✓ 似然比检验显示，大多数情况下添加行业分类没有显著改善模型\n")
} else {
  cat("   ✗ 似然比检验显示，大多数情况下添加行业分类显著改善了模型\n")
}

cat("\n")
cat("=== 研究假设验证 ===\n")
cat("研究假设：添加industry_type11会降低模型性能\n")

# 计算支持假设的证据比例
evidence_count <- 0
total_evidence <- 4

if(fixed_worse_count > total_models/2) evidence_count <- evidence_count + 1
if(random_worse_count > total_models/2) evidence_count <- evidence_count + 1
if(aic_improvements_fixed < total_models/2 && aic_improvements_random < total_models/2) evidence_count <- evidence_count + 1
if(significant_fixed < total_fixed_tests/2 && significant_random < total_random_tests/2) evidence_count <- evidence_count + 1

support_percentage <- round(evidence_count/total_evidence*100, 2)

cat("支持假设的证据比例:", support_percentage, "%\n")

if(support_percentage >= 75) {
  cat("结论：强烈支持研究假设 - 添加industry_type11显著降低了模型性能\n")
} else if(support_percentage >= 50) {
  cat("结论：部分支持研究假设 - 添加industry_type11在某些方面降低了模型性能\n")
} else {
  cat("结论：不支持研究假设 - 添加industry_type11总体上改善了模型性能\n")
}
```

# 原始混合效应模型 (LMER) - 保留原有分析

## 基础混合效应模型

```{r lmer_basic_11class_p3}
# 基础混合效应模型 - 省份随机效应
p3mix1_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = dta1_11class)

p3mix2_11 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = dta1_11class)

p3mix3_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = dta1_11class)

# 使用tab_model输出结果
tab_model(p3mix1_11, p3mix2_11, p3mix3_11,
          title = "P3 Mixed Effects Models (11-Class) - Province Random Effects",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

```{r lmer_basic_11class_p4}
# 中央/地方政治关联的混合效应模型
p4mix1_11_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = dta1_11class)

p4mix2_11_1 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                  (1 | PROVINCE/CITY), 
                  data = dta1_11class)

p4mix3_11_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = dta1_11class)

p4mix4_11_1 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                  (1 | PROVINCE/CITY) ,
                  data = dta1_11class)
```

```{r}
# 输出结果

tab_model(p4mix1_11_1, p4mix2_11_1, p4mix3_11_1, p4mix4_11_1,
          title = "P4 Mixed Effects Models (11-Class) - Central vs Local Connections",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

## 包含行业随机效应的混合效应模型

```{r}
p3mix1_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE/CITY) + (0 + connection_num | industry_type11),
                       data = dta1_11class)

p3mix2_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE/CITY) + (0 + connection_num | industry_type11),
                       data = dta1_11class)

p3mix3_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE/CITY) + (0 + connection_num | industry_type11),
                       data = dta1_11class)


# 中央/地方政治关联的混合效应模型
p4mix1_11 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 | industry_type11), 
                  data = dta1_11class)

p4mix2_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                  (1 | PROVINCE/CITY) + (0 + central_connection | industry_type11), 
                  data = dta1_11class)

p4mix3_11 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 | industry_type11), 
                  data = dta1_11class)

p4mix4_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                  (1 | PROVINCE/CITY) + (0 + local_connection | industry_type11),
                  data = dta1_11class)


# 输出随机斜率模型结果
tab_model(p3mix1_slope_11, p3mix2_slope_11, p3mix3_slope_11,
          title = "Random Slope Models (11-Class) - Industry-specific Interaction Effects",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix1_11, p4mix2_11, p4mix3_11, p4mix4_11,
          title = "P4 Mixed Effects Models (11-Class) - Central vs Local Connections",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)


```

## 包含行业固定效应的混合效应模型

```{r lmer_fixed_industry_11class}
# 包含行业固定效应的混合效应模型
p3mix1_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE/CITY),
                                data = dta1_11class)

p3mix2_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE/CITY),
                                data = dta1_11class)


p3mix3_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                                   ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                                   as.factor(industry_type11) + (1 | PROVINCE/CITY),
                                   data = dta1_11class)

p4mix1_fixed_central_11 <- lmer(Environmental_Information_Disclosure ~ central_connection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               as.factor(industry_type11) + (1 | PROVINCE/CITY),
                               data = dta1_11class)

p4mix2_fixed_central_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                               as.factor(industry_type11) + (1 | PROVINCE/CITY),
                               data = dta1_11class)

p4mix1_fixed_local_11 <- lmer(Environmental_Information_Disclosure ~ local_connection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             as.factor(industry_type11) + (1 | PROVINCE/CITY),
                             data = dta1_11class)
p4mix2_fixed_local_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                             as.factor(industry_type11) + (1 | PROVINCE/CITY),
                             data = dta1_11class)

# 输出结果
tab_model(p3mix1_fixed_industry_11, p3mix2_fixed_industry_11, p3mix3_fixed_industry_11,
          title = "Mixed Effects Models with Industry Fixed Effects (11-Class) - P3 Models",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix1_fixed_central_11, p4mix2_fixed_central_11, p4mix1_fixed_local_11, p4mix2_fixed_local_11,
          title = "Mixed Effects Models with Industry Fixed Effects (11-Class) - P4 Models",
          dv.labels = "Environmental Information Disclosure",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```