library(lme4)
library(lmerTest)
library(sjPlot)
library(ggplot2)
library(reshape2)
library(dplyr)
library(MuMIn)  # 用于计算R平方

# 加载数据
load("dta1_20240903.RData")

# 检查数据结构
cat("原始数据概览:\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")

# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # 1 Energy
  dta1$IndustryName %in% c("石油和天然气开采业",
                           "石油加工、炼焦及核燃料加工业", "开发辅助活动", 
                           "煤炭开采和洗选业")                                   ~ "Energy",
  
  # 2 Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                           "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                           "木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业",
                           "金属制品业")                                         ~ "Materials",
  
  # 3 Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业",
                           "电气机械及器材制造业","通用设备制造业",
                           "装卸搬运和运输代理业","道路运输业","水上运输业",
                           "航空运输业","铁路运输业","仓储业","其他制造业",
                           "专业技术服务业","其他服务业",
                           "废弃资源综合利用业", "综合", "金属制品、机械和设备修理业",
                           "邮政业", "仪器仪表制造业",
                           # 建筑／环保
                           "房屋建筑业","建筑安装业","土木工程建筑业",
                           "建筑装饰和其他建筑业","生态保护和环境治理业",
                           "公共设施管理业")                                     ~ "Industrials",
  
  # 4 Consumer Discretionary
  dta1$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业",
                           "教育","纺织业","零售业","纺织服装、服饰业",
                           "文教、工美、体育和娱乐用品制造业","文化艺术业",
                           "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业",
                           "机动车、电子产品和日用产品修理业",
                           "汽车制造业")                                         ~ "Consumer Discretionary",
  
  # 5 Consumer Staples
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业",
                           "农业","畜牧业","渔业",
                           "农、林、牧、渔服务业")                               ~ "Consumer Staples",
  
  # 6 Health Care
  dta1$IndustryName %in% c("医药制造业","卫生")                                 ~ "Health Care",
  
  # 7 Financials
  dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业",
                           "其他金融业","租赁业","商务服务业")                   ~ "Financials",
  
  # 8 Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业",
                           "计算机、通信和其他电子设备制造业",
                           "印刷和记录媒介复制业",
                           "研究和试验发展","科技推广和应用服务业")               ~ "Information Technology",
  
  # 9 Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务",
                           "广播、电视、电影和影视录音制作业",
                           "新闻和出版业","互联网和相关服务")                     ~ "Communication Services",
  
  # 10 Utilities
  dta1$IndustryName %in% c("电力、热力生产和供应业",
                           "燃气生产和供应业",
                           "水的生产和供应业")                                   ~ "Utilities",
  
  # 11 Real Estate
  dta1$IndustryName %in% c("房地产业")                            ~ "Real Estate",
  
  TRUE ~ NA_character_
)


# 显示分类结果
cat("11类分类结果:\n")
industry_table <- table(dta1$industry_type11, useNA = "always")
print(industry_table)

# 分析NA值的详细情况
na_data <- dta1 %>% filter(is.na(industry_type11))
cat("\n=== NA值分析 ===\n")
cat("NA观测数:", nrow(na_data), "\n")
cat("NA占总观测数的比例:", round(nrow(na_data)/nrow(dta1)*100, 2), "%\n")
cat("NA涉及的唯一公司数:", length(unique(na_data$Symbol)), "\n")
cat("NA涉及的唯一行业数:", length(unique(na_data$IndustryName)), "\n")

# 显示所有未分类的行业名称
cat("\n未分类的行业名称:\n")
na_industries <- na_data %>%
  group_by(IndustryName) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(desc(count))
print(na_industries)

# 创建去除NA的数据集用于分析
dta1_11class <- dta1 %>% filter(!is.na(industry_type11))
cat("\n去除NA后的观测数:", nrow(dta1_11class), "\n")
cat("去除NA后的公司数:", length(unique(dta1_11class$Symbol)), "\n")
# ============================================================================
# 模型比较分析 - 随机斜率模型 vs 嵌套模型
# ============================================================================

# 模型1: 随机斜率模型 - 行业层面的政治关联随机斜率
p3mix_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) +
                       (1 | PROVINCE) + (1 | industry_type11),
                       data = dta1_11class)

# 模型2: 嵌套模型 - 省份/城市嵌套结构
p3mix_nested_industry_11 <- lmer(Environmental_Information_Disclosure ~  after_first_inspection *connection_num + ROA + ESG_Rate + Leverage +
                                 RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY),
                                 data = dta1_11class)

# ============================================================================
# 模型比较指标计算
# ============================================================================

# 提取模型拟合指标
model_comparison <- data.frame(
  Model = c("Random Slope Model", "Nested Model"),
  AIC = c(AIC(p3mix_slope_11), AIC(p3mix_nested_industry_11)),
  BIC = c(BIC(p3mix_slope_11), BIC(p3mix_nested_industry_11)),
  LogLik = c(logLik(p3mix_slope_11), logLik(p3mix_nested_industry_11)),
  Deviance = c(deviance(p3mix_slope_11), deviance(p3mix_nested_industry_11))
)

# 计算R平方 (使用MuMIn包)
r2_slope <- r.squaredGLMM(p3mix_slope_11)
r2_nested <- r.squaredGLMM(p3mix_nested_industry_11)

model_comparison$R2_marginal <- c(r2_slope[1], r2_nested[1])
model_comparison$R2_conditional <- c(r2_slope[2], r2_nested[2])

# 显示比较结果
print("=== 模型比较结果 ===")
print(model_comparison)

# ============================================================================
# 似然比检验 (Likelihood Ratio Test)
# ============================================================================

# 注意：只有当模型是嵌套关系时才能进行LR检验
# 这两个模型的随机效应结构不同，不是严格的嵌套关系
# 但我们可以尝试比较它们的对数似然值

cat("\n=== 似然比检验信息 ===\n")
cat("模型1 (Random Slope) Log-Likelihood:", as.numeric(logLik(p3mix_slope_11)), "\n")
cat("模型2 (Nested) Log-Likelihood:", as.numeric(logLik(p3mix_nested_industry_11)), "\n")
cat("Log-Likelihood差异:", as.numeric(logLik(p3mix_slope_11)) - as.numeric(logLik(p3mix_nested_industry_11)), "\n")

# 如果模型是嵌套的，可以使用anova()进行正式的LR检验
# anova(p3mix_slope_11, p3mix_nested_industry_11)

# ============================================================================
# 详细的模型比较表格
# ============================================================================

# 使用sjPlot包创建比较表格
tab_model(p3mix_slope_11, p3mix_nested_industry_11,
          title = "模型比较：随机斜率模型 vs 嵌套模型",
          dv.labels = c("随机斜率模型", "嵌套模型"),
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE,
          show.aic = TRUE,
          show.loglik = TRUE,
          show.dev = TRUE)

# ============================================================================
# 模型选择建议
# ============================================================================

cat("\n=== 模型选择建议 ===\n")

# AIC比较 (越小越好)
if(model_comparison$AIC[1] < model_comparison$AIC[2]) {
  cat("根据AIC：随机斜率模型更优 (AIC =", round(model_comparison$AIC[1], 2), ")\n")
} else {
  cat("根据AIC：嵌套模型更优 (AIC =", round(model_comparison$AIC[2], 2), ")\n")
}

# BIC比较 (越小越好)
if(model_comparison$BIC[1] < model_comparison$BIC[2]) {
  cat("根据BIC：随机斜率模型更优 (BIC =", round(model_comparison$BIC[1], 2), ")\n")
} else {
  cat("根据BIC：嵌套模型更优 (BIC =", round(model_comparison$BIC[2], 2), ")\n")
}

# R²比较 (越大越好)
if(model_comparison$R2_conditional[1] > model_comparison$R2_conditional[2]) {
  cat("根据条件R²：随机斜率模型更优 (R² =", round(model_comparison$R2_conditional[1], 3), ")\n")
} else {
  cat("根据条件R²：嵌套模型更优 (R² =", round(model_comparison$R2_conditional[2], 3), ")\n")
}

# ============================================================================
# 可视化比较
# ============================================================================

# 创建比较图表

# 准备数据用于可视化
comparison_long <- melt(model_comparison[,c("Model", "AIC", "BIC")], 
                       id.vars = "Model", 
                       variable.name = "Metric", 
                       value.name = "Value")

# 创建比较图
p1 <- ggplot(comparison_long, aes(x = Model, y = Value, fill = Metric)) +
  geom_bar(stat = "identity", position = "dodge") +
  facet_wrap(~Metric, scales = "free_y") +
  theme_minimal() +
  labs(title = "模型比较：AIC和BIC",
       x = "模型类型",
       y = "指标值") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

print(p1)

# R²比较图
r2_data <- data.frame(
  Model = rep(model_comparison$Model, 2),
  R2_Type = rep(c("Marginal", "Conditional"), each = 2),
  R2_Value = c(model_comparison$R2_marginal, model_comparison$R2_conditional)
)

p2 <- ggplot(r2_data, aes(x = Model, y = R2_Value, fill = R2_Type)) +
  geom_bar(stat = "identity", position = "dodge") +
  theme_minimal() +
  labs(title = "模型比较：R²值",
       x = "模型类型",
       y = "R²值",
       fill = "R²类型") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

print(p2)
