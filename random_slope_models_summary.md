# 随机斜率模型总结报告

## 模型设计理念

根据您的要求，我们在行业层面使用了随机斜率而不是随机截距。这种设计的理论依据是：

1. **省份层面使用随机截距** `(1 | PROVINCE)`: 不同省份在环境信息披露的基础水平上存在差异
2. **行业层面使用随机斜率** `(0 + variable | industry)`: 政治关联对环境信息披露的影响在不同行业间存在异质性

## 添加的随机斜率模型

### 11类行业分类模型

#### P3 随机斜率模型
```r
p3mix_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                       (1 | PROVINCE) + (0 + connection_num | industry_type11), 
                       data = dta1_11class)
```

#### P4 中央政治关联随机斜率模型
```r
p4mix_central_slope_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                               (1 | PROVINCE) + (0 + central_connection | industry_type11), 
                               data = dta1_11class)
```

#### P4 地方政治关联随机斜率模型
```r
p4mix_local_slope_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + 
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                             (1 | PROVINCE) + (0 + local_connection | industry_type11), 
                             data = dta1_11class)
```

### 13类行业分类模型

#### P3 随机斜率模型
```r
p3mix_slope_13 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                       ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                       (1 | PROVINCE) + (0 + connection_num | industry13), 
                       data = dta1)
```

#### P4 中央政治关联随机斜率模型
```r
p4mix_central_slope_13 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                               ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                               (1 | PROVINCE) + (0 + central_connection | industry13), 
                               data = dta1)
```

#### P4 地方政治关联随机斜率模型
```r
p4mix_local_slope_13 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + 
                             ESG_Rate + ROA + Leverage + RegisterCapital_log + as.factor(EndYear) + 
                             (1 | PROVINCE) + (0 + local_connection | industry13), 
                             data = dta1)
```

## 模型解释

### 随机效应结构
- `(1 | PROVINCE)`: 省份随机截距，允许不同省份有不同的基础披露水平
- `(0 + variable | industry)`: 行业随机斜率，允许政治关联变量的效应在不同行业间变化

### 理论意义
1. **行业异质性**: 不同行业对政治关联的敏感性不同
2. **政策效应差异**: 环保政策对不同行业的影响程度不同
3. **监管强度**: 不同行业面临的环保监管强度不同

## 模型优势

### 相比随机截距模型的优势
1. **更好地捕捉行业异质性**: 允许政治关联效应在行业间变化
2. **更符合理论预期**: 不同行业的政治关联作用机制确实不同
3. **更精确的估计**: 通过建模异质性减少估计偏误

### 相比固定效应模型的优势
1. **保留行业信息**: 不会完全消除行业层面的变异
2. **更好的统计效率**: 利用行业间和行业内的变异
3. **可以估计行业层面效应**: 提供行业异质性的定量信息

## 实施细节

### 模型收敛性
- 初始尝试使用交互项的随机斜率遇到收敛问题
- 简化为单个变量的随机斜率，成功收敛
- 这是混合效应模型中常见的权衡

### 模型比较
所有分析文件都包含了以下模型的比较：
1. PLM固定效应模型
2. LMER随机截距模型
3. LMER随机斜率模型

### 输出内容
每个分析文件都包含：
1. 模型系数表格
2. 模型拟合度指标（R²边际、R²条件、ICC）
3. 随机效应方差分析
4. 模型比较表格

## 文件更新

### 更新的文件列表
1. `industry_11class_analysis.qmd` - 11类分析完整文档
2. `industry_13class_analysis.qmd` - 13类分析完整文档
3. `run_11class_analysis.R` - 11类分析可执行脚本
4. `run_13class_analysis.R` - 13类分析可执行脚本

### 生成的HTML文件
1. `industry_11class_analysis.html` - 11类分析结果
2. `industry_13class_analysis.html` - 13类分析结果

## 主要发现预期

### 随机斜率模型的预期优势
1. **更高的条件R²**: 通过建模行业异质性提高解释力
2. **更准确的政治关联效应估计**: 考虑行业差异后的精确估计
3. **行业异质性量化**: 提供不同行业政治关联效应的具体数值

### 政策含义
1. **差异化政策**: 支持针对不同行业制定差异化环保政策
2. **监管重点**: 识别政治关联效应较强的行业作为监管重点
3. **制度设计**: 为环境信息披露制度的行业差异化设计提供依据

## 技术说明

- 所有模型都控制了年份固定效应
- 省份层面使用随机截距，行业层面使用随机斜率
- 使用REML估计方法
- 通过performance包计算模型拟合度指标
- 使用sjPlot包生成专业的模型输出表格

这种设计更好地反映了您关于行业异质性的理论预期，即政治关联的效应在不同行业间存在系统性差异。
