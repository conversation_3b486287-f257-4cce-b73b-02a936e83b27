#reading the file

: dta1=dta1_20240903.RData

```{r load_data}
# Load the data file
load("dta1_20240903.RData")
# You can add a quick check to confirm the data loaded correctly

```

#model 1
```{r P3 connection (continuous)}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(plm)
p3way1 <- plm(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + as.factor(PROVINCE) + RegisterCapital_log + ESG_Rate, data=dta1,
              index=c("EndYear"),model="within")

summary(p3way1)


p3way2 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate  + as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta1,
              index=c( "EndYear"),model="within")

p3way3 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  + as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta1,
              index=c("EndYear"),model="within")

library(stargazer)
stargazer(p3way1, p3way2, p3way3,
          type="text", 
          column.labels = c("Year", "Individual", "Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


```

#model 2
```{r P4 contral/local connection (continuous)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p4m1 <- plm(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate  + RegisterCapital_log + as.factor(PROVINCE) + ROA + Leverage, data=dta1,
            index=c( "EndYear"),model="within")

p4m2 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate  + as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage, data=dta1,
            index=c("EndYear"),model="within")

p4m3 <- plm(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate  + RegisterCapital_log + as.factor(PROVINCE) + ROA + Leverage, data=dta1,
            index=c("EndYear"),model="within")

p4m4 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage, data=dta1,
            index=c("EndYear"),model="within")

library(stargazer)
stargazer(p4m1, p4m2, p4m3,p4m4,
          type="text", 
          column.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```

#correlation matrix of key variables
```{r}
# Create a correlation matrix of key variables
library(corrplot)
library(ggplot2)
library(reshape2)

# Select variables for correlation analysis
corr_vars <- dta1[, c("Environmental_Information_Disclosure", "connection_num", 
                      "central_connection", "local_connection", "after_first_inspection",
                      "Age", "ROA", "ESG_Rate", "Leverage", "RegisterCapital_log")]

# 创建更易读的变量名
var_names <- c("Environmental\nDisclosure", "Connection\nNumber", 
               "Central\nConnection", "Local\nConnection", "After First\nInspection",
               "Age", "ROA", "ESG Rate", "Leverage", "Register\nCapital (log)")

# 设置列名以便在图中使用
colnames(corr_vars) <- var_names

# Calculate correlation matrix
cor_matrix <- cor(corr_vars, use = "pairwise.complete.obs")

# Print correlation matrix with scientific formatting
print(round(cor_matrix, 3), quote = FALSE)

# Create publication-quality correlation plot
# Calculate p-values
library(Hmisc)
cor_test <- rcorr(as.matrix(corr_vars))

# Melt the correlation matrix for ggplot
melted_cor <- melt(cor_matrix)

# Add p-values to the melted dataframe
melted_cor$p_value <- melt(cor_test$P)$value

# Create the ggplot visualization with grayscale color scheme
p <- ggplot(data = melted_cor, aes(x = Var1, y = Var2, fill = value)) +
  geom_tile(color = "white") +
  geom_text(aes(label = ifelse(p_value < 0.05, 
                              sprintf("%.2f*", value), 
                              sprintf("%.2f", value))), 
            size = 3) +
  scale_fill_gradient2(low = "gray20", high = "gray80", mid = "white", 
                       midpoint = 0, limit = c(-1,1), name = "Correlation") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, vjust = 1, hjust = 1, size = 8),
        axis.text.y = element_text(size = 8),
        axis.title = element_blank(),
        panel.grid.major = element_blank(),
        legend.position = "bottom") +
  coord_fixed() +
  labs(caption = "* indicates p < 0.05")

# Display the plot
print(p)

# Save as SVG for publication (vector format, maintains quality at any size)
ggsave("d:/Research/ESG/ESG_China/R-ESG China/correlation_matrix_gray.svg", 
       plot = p, width = 8, height = 7, dpi = 300)
```
彩色
```{r}
# Create a correlation matrix of key variables
library(corrplot)
library(ggplot2)
library(reshape2)

# Select variables for correlation analysis
corr_vars <- dta1[, c("Environmental_Information_Disclosure", "connection_num", 
                      "central_connection", "local_connection", "after_first_inspection",
                      "Age", "ROA", "ESG_Rate", "Leverage", "RegisterCapital_log")]

# 创建更易读的变量名
var_names <- c("Environmental\nDisclosure", "Connection\nNumber", 
               "Central\nConnection", "Local\nConnection", "After First\nInspection",
               "Age", "ROA", "ESG Rate", "Leverage", "Register\nCapital (log)")

# 设置列名以便在图中使用
colnames(corr_vars) <- var_names

# Calculate correlation matrix
cor_matrix <- cor(corr_vars, use = "pairwise.complete.obs")

# Print correlation matrix with scientific formatting
print(round(cor_matrix, 3), quote = FALSE)

# Create publication-quality correlation plot
# Calculate p-values
library(Hmisc)
cor_test <- rcorr(as.matrix(corr_vars))

# Melt the correlation matrix for ggplot
melted_cor <- melt(cor_matrix)

# Add p-values to the melted dataframe
melted_cor$p_value <- melt(cor_test$P)$value

# Create the ggplot visualization with scientific journal color scheme
p <- ggplot(data = melted_cor, aes(x = Var1, y = Var2, fill = value)) +
  geom_tile(color = "white") +
  geom_text(aes(label = ifelse(p_value < 0.05, 
                              sprintf("%.2f*", value), 
                              sprintf("%.2f", value))),
            color = ifelse(abs(melted_cor$value) > 0.5, "white", "black"), 
            size = 3) +
  scale_fill_gradient2(low = "#053061", mid = "white", high = "#67001F", 
                       midpoint = 0, limit = c(-1,1), name = "Correlation") +
  theme_bw() +
  theme(axis.text.x = element_text(angle = 45, vjust = 1, hjust = 1, size = 9, face = "bold"),
        axis.text.y = element_text(size = 9, face = "bold"),
        axis.title = element_blank(),
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        panel.border = element_rect(color = "black", fill = NA, size = 0.5),
        legend.position = "bottom",
        legend.title = element_text(face = "bold"),
        plot.title = element_text(face = "bold", hjust = 0.5, size = 12),
        plot.caption = element_text(hjust = 0, size = 9, face = "italic")) +
  coord_fixed() +
  labs(title = "Correlation Matrix of Key Variables",
       caption = "* indicates p < 0.05")

# Display the plot
print(p)

# Save as high-resolution PDF for publication (vector format, maintains quality at any size)
ggsave("d:/Research/ESG/ESG_China/R-ESG China/correlation_matrix_color.pdf", 
       plot = p, width = 8, height = 7, dpi = 300)

ggsave("d:/Research/ESG/ESG_China/R-ESG China/correlation_matrix_color.svg", 
       plot = p, width = 8, height = 7, dpi = 300)
```


# After loading the data
```{r sample_description}
# Calculate number of unique firms and total firm-year observations
n_firms <- length(unique(dta1$Symbol))
n_observations <- nrow(dta1)

# Create a publication-quality table for sample description
library(kableExtra)
library(dplyr)

# Create sample description dataframe
sample_df <- data.frame(
  Description = c("Number of unique firms", "Number of firm-year observations"),
  Count = c(n_firms, n_observations)
)

# Print the table in a clean format suitable for Science journal
kable(sample_df, format = "html", col.names = c("Description", "Count"), 
      align = c("l", "r"), caption = "Sample Composition") %>%
  kable_styling(bootstrap_options = c("striped", "condensed"), 
                full_width = FALSE, position = "left",
                font_size = 12) %>%
  row_spec(0, bold = TRUE)

# Additional sample information (optional)
# Time period
year_range <- range(dta1$EndYear, na.rm = TRUE)
cat(sprintf("The sample covers %d firms over the period %d to %d, resulting in %d firm-year observations.",
            n_firms, year_range[1], year_range[2], n_observations))
```



# Multilevel models - Province
```{r multilevel_models}
library(lme4)
library(lmerTest)
library(sjPlot)  # For better handling of mixed models

# Create multilevel models for political connections


p3way3 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  + as.factor(industry13)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta1,model="within")

p3mix1 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection *connection_num + ROA + ESG_Rate + Leverage + 
               RegisterCapital_log+as.factor(EndYear)+(1 | PROVINCE)+ (1| industry13), data = dta1,REML = TRUE)




# AIC/BIC 对比
aic_p3way3 <- AIC(logLik(p3way3))
bic_p3way3 <- BIC(logLik(p3way3))

aic_p3mix1 <- AIC(p3mix1)
bic_p3mix1 <- BIC(p3mix1)

# 输出结果
cat("AIC of p3way3 (fixed effects):", aic_p3way3, "\n")
cat("BIC of p3way3 (fixed effects):", bic_p3way3, "\n")
cat("AIC of p3mix1 (random effects):", aic_p3mix1, "\n")
cat("BIC of p3mix1 (random effects):", bic_p3mix1, "\n")





anova(p3mix1, p3way1)

summary(p3way3)

icc_value <- performance::icc(p3mix1)
icc_value




p3way1_random <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  + RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                     data = dta1, 
                     index = c("PROVINCE", "EndYear"), 
                     model = "random", 
                     effect = "twoways")

summary(p3way1_random)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection *connection_num + ROA + ESG_Rate + Leverage + 
               RegisterCapital_log+(1 | PROVINCE)+ (1| EndYear), data = dta1,REML = FALSE)

summary(p3way3)


stargazer(p3way1_random,p3way3,title = "Regression Results",omit="as.factor", align = TRUE, type = "text")



```

```{r}
p3mix1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num  + ESG_Rate + ROA + Leverage + 
                         RegisterCapital_log + as.factor(EndYear) +(1 | PROVINCE)+ (0+connection_num| industry_type11), 
                         data = dta1)






p3mix2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log+as.factor(EndYear)+(1 | PROVINCE) + (1| industry_type11), data = dta1)

p3mix3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log+as.factor(EndYear) +(1 | PROVINCE)+ (0+after_first_inspection * connection_num| industry_type11), data = dta1)
summary(p3mix3)


# Use tab_model instead of stargazer for mixed models
tab_model(p3mix1, p3mix2, p3mix3,
          title = "Multilevel Models for Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Connection Number", "ROA", "ESG Rate", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Connection Number × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

# Add multilevel models for central/local connections
p4mix1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log +as.factor(EndYear)+(1 | PROVINCE), data = dta1)
p4mix1_com <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log +as.factor(EndYear)+(1 | PROVINCE) +(0+central_connection| industry_type11), data = dta1)


p4mix2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log +as.factor(EndYear)+(1 | PROVINCE)+(0+central_connection * after_first_inspection| industry_type11), data = dta1)

p4mix3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + as.factor(EndYear)+(1 | PROVINCE)+(1 |industry_type11), data = dta1)

p4mix4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + as.factor(EndYear)+(1 | PROVINCE)+(0+local_connection * after_first_inspection| industry_type11), data = dta1)


anova(p4mix1, p4mix1_com)


summary(p4mix4)


# 筛选子组数据：industry_type11 == "Materials"
materials_data <- dta1 %>% 
  filter(industry13 == "Communication and Culture")

# 在子组数据上拟合混合效应模型
p4mix1_materials <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num  + ESG_Rate + ROA + Leverage + 
                         RegisterCapital_log + as.factor(EndYear) +(1 | CITY)+as.factor(industry_type11), 
                         data = dta1)
summary(p4mix1_materials)




icc_value <- performance::icc(p4mix1_materials)


icc_value


p4mix1_materials <- lm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num  + ESG_Rate + ROA + Leverage + 
                         RegisterCapital_log + as.factor(EndYear) , 
                         data = materials_data)



summary(p4mix1_materials)
unique(materials_data$Symbol)

    table(materials_data$connection_num)

```

```{r}



library(lmtest)
lrtest(p4mix1, p4mix1_1)


unique(dta1$industry13)

#ICC
# 安装并加载 performance 包
install.packages("performance")
library(performance)

# 使用前面拟合的模型
icc_value <- performance::icc(p3mix1)
icc_value


 library(stargazer)
    stargazer(p4mix1, p4mix2, p4mix3,p4mix4,title = "Regression Results", align = TRUE, type = "text")

    
    library(dplyr)
#industry
    dta1$industry_type11 <- NA  # 初始化为空

# 定义分类规则
dta1$industry_type11 <- case_when(
  # Energy
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "石油加工、炼焦及核燃料加工业", "石油和天然气开采业", 
                           "煤炭开采和洗选业") ~ "Energy",
  
  # Consumer Discretionary
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业", "住宿业", 
                           "餐饮业", "体育", 
                           "纺织服装、服饰业", "文教、工美、体育和娱乐用品制造业", 
                           "文化艺术业", "皮革、毛皮、羽毛及其制品和制鞋业", 
                           "家具制造业", "机动车、电子产品和日用产品修理业") ~ "Consumer Discretionary",
  
  # Real Estate
  dta1$IndustryName %in% c("房地产业", "房屋建筑业", 
                           "建筑安装业", "土木工程建筑业", 
                           "建筑装饰和其他建筑业") ~ "Real Estate",
  
  # Utilities
  dta1$IndustryName %in% c("水的生产和供应业", "公共设施管理业", 
                           "生态保护和环境治理业") ~ "Utilities",
  
  # Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "互联网和相关服务", 
                           "邮政业") ~ "Communication Services",
  
  # Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "计算机、通信和其他电子设备制造业", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业", "专用设备制造业", 
                           "通用设备制造业", "仪器仪表制造业", 
                           "装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "航空运输业", 
                           "铁路运输业", "仓储业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "金属制品业", "其他制造业", 
                           "橡胶和塑料制品业", "木材加工及木、竹、藤、棕、草制品业", 
                           "废弃资源综合利用业", "金属制品、机械和设备修理业") ~ "Industrials",
  
  # Consumer Staples
  dta1$IndustryName %in% c("食品制造业", "农副食品加工业", 
                           "畜牧业", "渔业", 
                           "农业", "林业", 
                           "农、林、牧、渔服务业") ~ "Consumer Staples",
  
  # Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业", "化学纤维制造业", 
                           "非金属矿物制品业", "造纸及纸制品业", 
                           "印刷和记录媒介复制业") ~ "Materials",
  
  # Health Care
  dta1$IndustryName %in% c("医药制造业", "卫生") ~ "Health Care",
  
  # Financials
  dta1$IndustryName %in% c("货币金融服务", "商务服务业", 
                           "资本市场服务", "其他金融业", 
                           "保险业", "租赁业") ~ "Financials",
)


# 创建一个新的变量 industry13
dta1$industry13 <- NA  # 初始化为空

# 定义分类规则
dta1$industry13 <- case_when(
  # 1. Agriculture, Forestry, Livestock Farming, Fishery
  dta1$IndustryName %in% c("农业", "林业", "畜牧业", "渔业", 
                           "农、林、牧、渔服务业") ~ "Agriculture, Forestry, Livestock Farming, Fishery",
  
  # 2. Mining
  dta1$IndustryName %in% c("煤炭开采和洗选业", "石油和天然气开采业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "开采辅助活动") ~ "Mining",
  
  # 3. Manufacturing
  dta1$IndustryName %in% c("医药制造业", "化学原料及化学制品制造业", 
                           "化学纤维制造业", "非金属矿物制品业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "金属制品业", "通用设备制造业", 
                           "专用设备制造业", "铁路、船舶、航空航天和其它运输设备制造业", 
                           "汽车制造业", "电气机械及器材制造业", 
                           "计算机、通信和其他电子设备制造业", "仪器仪表制造业", 
                           "其他制造业", "食品制造业", 
                           "酒、饮料和精制茶制造业", "农副食品加工业", 
                           "纺织业", "纺织服装、服饰业", 
                           "皮革、毛皮、羽毛及其制品和制鞋业", "木材加工及木、竹、藤、棕、草制品业", 
                           "造纸及纸制品业", "印刷和记录媒介复制业", 
                           "橡胶和塑料制品业", "家具制造业", 
                           "废弃资源综合利用业", "文教、工美、体育和娱乐用品制造业", 
                           "金属制品、机械和设备修理业", "石油加工、炼焦及核燃料加工业") ~ "Manufacturing",
  
  # 4. Electric Power, Gas, and Water Production and Supply
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "水的生产和供应业") ~ "Electric Power, Gas, and Water Production and Supply",
  
  # 5. Construction
  dta1$IndustryName %in% c("房屋建筑业", "建筑安装业", 
                           "土木工程建筑业", "建筑装饰和其他建筑业") ~ "Construction",
  
  # 6. Transport and Storage
  dta1$IndustryName %in% c("装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "铁路运输业", 
                           "航空运输业", "仓储业") ~ "Transport and Storage",
  
  # 7. Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "互联网和相关服务", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # 8. Wholesale and Retail Trade
  dta1$IndustryName %in% c("批发业", "零售业") ~ "Wholesale and Retail Trade",
  
  # 9. Finance and Insurance
  dta1$IndustryName %in% c("货币金融服务", "资本市场服务", 
                           "其他金融业", "保险业", 
                           "租赁业") ~ "Finance and Insurance",
  
  # 10. Real Estate
  dta1$IndustryName %in% c("房地产业") ~ "Real Estate",
  
  # 11. Social Service
  dta1$IndustryName %in% c("教育", "卫生", 
                           "公共设施管理业", "生态保护和环境治理业", 
                           "居民服务业") ~ "Social Service",
  
  # 12. Communication and Culture
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "文化艺术业", 
                           "体育", "邮政业") ~ "Communication and Culture",
  
  # 13. Others
  TRUE ~ "Others"
)



# Use tab_model for central/local connection models
tab_model(p4mix1, p4mix2, p4mix3, p4mix4,
          title = "Multilevel Models for Central vs Local Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Central Connection", "ESG Rate", "ROA", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Central Connection × After First Inspection",
                         "Local Connection", "Local Connection × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

# For comparison with fixed effects models, use texreg package
library(texreg)
screenreg(list(p3mix1, p3mix2, p3mix3), 
          custom.model.names = c("Model 1", "Model 2", "Model 3"),
          custom.coef.names = c("Intercept", "Age", "Connection Number", "ROA", "ESG Rate", "Leverage", 
                               "Register Capital (log)", "After First Inspection", 
                               "Connection Number × After First Inspection"),
          title = "Multilevel Models for Political Connection Effects")


```

# Multilevel models - City
```{r multilevel_models City}
library(lme4)
library(lmerTest)
library(sjPlot)  # For better handling of mixed models

# Create multilevel models for political connections
p3mix1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + 
               RegisterCapital_log + (1 | CITY), data = dta1)

p3mix2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | CITY), data = dta1)

p3mix3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | CITY), data = dta1)

# Use tab_model instead of stargazer for mixed models
tab_model(p3mix1, p3mix2, p3mix3,
          title = "Multilevel Models for Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Connection Number", "ROA", "ESG Rate", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Connection Number × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

# Add multilevel models for central/local connections
p4mix1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | CITY), data = dta1)

p4mix2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | CITY), data = dta1)

p4mix3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | CITY), data = dta1)

p4mix4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | CITY), data = dta1)

# Use tab_model for central/local connection models
tab_model(p4mix1, p4mix2, p4mix3, p4mix4,
          title = "Multilevel Models for Central vs Local Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Central Connection", "ESG Rate", "ROA", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Central Connection × After First Inspection",
                         "Local Connection", "Local Connection × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

```


# Multilevel models - Province City
```{r multilevel_models City}
library(lme4)
library(lmerTest)
library(sjPlot)  # For better handling of mixed models

# Create multilevel models for political connections
p3mix1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + 
               RegisterCapital_log + (1 | PROVINCE) + (1 | CITY), data = dta1)

p3mix2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE) + (1 | CITY), data = dta1)

p3mix3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE) + (1 | CITY), data = dta1)

# Use tab_model instead of stargazer for mixed models
tab_model(p3mix1, p3mix2, p3mix3,
          title = "Multilevel Models for Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Connection Number", "ROA", "ESG Rate", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Connection Number × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

# Add multilevel models for central/local connections
p4mix1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE) + (1 | CITY), data = dta1)

p4mix2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE) + (1 | CITY), data = dta1)

p4mix3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE) + (1 | CITY), data = dta1)

p4mix4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE) + (1 | CITY), data = dta1)

# Use tab_model for central/local connection models
tab_model(p4mix1, p4mix2, p4mix3, p4mix4,
          title = "Multilevel Models for Central vs Local Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Central Connection", "ESG Rate", "ROA", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Central Connection × After First Inspection",
                         "Local Connection", "Local Connection × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

```



# Multilevel models - Province+City
```{r multilevel_models+City}
library(lme4)
library(lmerTest)
library(texreg)
library(sjPlot)  # For better handling of mixed models

# Create multilevel models for political connections
p3mix1 <- lmer(Environmental_Information_Disclosure ~ connection_num + ROA + ESG_Rate + Leverage + 
               RegisterCapital_log + (1 | PROVINCE/CITY), data = dta1)

p3mix2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE/CITY), data = dta1)

p3mix3 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection * connection_num + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE/CITY), data = dta1)

# Use tab_model instead of stargazer for mixed models
tab_model(p3mix1, p3mix2, p3mix3,
          title = "Multilevel Models for Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Connection Number", "ROA", "Env Rate", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Connection Number × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE,
          file = "d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p3.html")  # 保存为HTML文件

# Add multilevel models for central/local connections
p4mix1 <- lmer(Environmental_Information_Disclosure ~ central_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE/CITY), data = dta1)

p4mix2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE/CITY), data = dta1)

p4mix3 <- lmer(Environmental_Information_Disclosure ~ local_connection + ESG_Rate + ROA + Leverage + 
               RegisterCapital_log + (1 | PROVINCE/CITY), data = dta1)

p4mix4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + 
               ROA + Leverage + RegisterCapital_log + (1 | PROVINCE/CITY), data = dta1)

# Use tab_model for central/local connection models
tab_model(p4mix1, p4mix2, p4mix3, p4mix4,
          title = "Multilevel Models for Central vs Local Political Connection Effects",
          dv.labels = "Environmental Information Disclosure",
          pred.labels = c("Intercept", "Age", "Central Connection", "Env Rate", "ROA", "Leverage", 
                         "Register Capital (log)", "After First Inspection", 
                         "Central Connection × After First Inspection",
                         "Local Connection", "Local Connection × After First Inspection"),
          string.pred = "Predictors",
          string.ci = "CI (95%)",
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE,
          file = "d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p4.html")  # 保存为HTML文件

          
# If you want HTML output for publication, use htmlreg instead
htmlreg(list(p3mix1, p3mix2, p3mix3), file = "d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p1.html")
htmlreg(list(p4mix1, p4mix2, p4mix3, p4mix4), file = "d:/Research/ESG/ESG_China/R-ESG China/multilevel_models_p2.html")


```
# E -> ESG
E这个指标换成ESG这三个的指标

### Read data
```{r}
library(readxl)
library(dplyr)

# 使用na参数指定如何处理#N/A值，并使用col_types参数指定列类型
bloomberg_data <- read_excel("esg_bloomberg.xlsx", 
                            sheet = "Data", 
                            col_names = FALSE,
                            na = c("", "NA", "#N/A"), # 将#N/A识别为NA值
                            col_types = "text") # 将所有列都作为文本读入，避免类型转换问题

bloomberg_data = bloomberg_data[-1,]
names(bloomberg_data) = c("stockID", "type", "2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021")

# 将年份列转换为数值型
year_cols <- c("2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021")
bloomberg_data[year_cols] <- lapply(bloomberg_data[year_cols], function(x) as.numeric(x))

# melt bloomberg_data
library(data.table)
bloomberg_data_long = melt(setDT(bloomberg_data), id.vars = c("stockID","type"), variable.name = "year")

library(stringr)
bloomberg_data_long$stockID <- str_replace(bloomberg_data_long$stockID, " CH Equity", "")
names(bloomberg_data_long) = c("Symbol", "Type", "EndYear", "BloobergESGValue")

## merge
bloomberg_data_long$EndYear = as.character(bloomberg_data_long$EndYear)
bloomberg_data_long$EndYear <- as.Date(paste(bloomberg_data_long$EndYear, "-12-31", sep = ""))
bloomberg_data_long = bloomberg_data_long[bloomberg_data_long$Type=="ESG_DISCLOSURE_SCORE", ]

# 确保BloobergESGValue是数值型
bloomberg_data_long$BloobergESGValue <- as.numeric(bloomberg_data_long$BloobergESGValue)

dta2 = left_join(bloomberg_data_long, dta1, by = c("Symbol", "EndYear")) 

missing_esg = dta1[!dta1$Symbol %in% dta2$Symbol ,]  # 没有ESG分数的公司
```
```{r}
colnames(dta2)[colnames(dta2) == "BloobergESGValue"] <- "ESG_Information_Disclosure"
dta2 <- subset(dta2, select = -c(Type.x, Type.y))

```


### P1 connection (dummy)

```{r P1 connection (dummy)}
#P1

library(plm)
p1way1 <- plm(ESG_Information_Disclosure ~ Age + connections_dummy +  ROA + ESG_Rate + Leverage + RegisterCapital_log + as.factor(IndustryName)+ as.factor(PROVINCE) + ESG_Rate, data=dta2,
              index=c("Symbol", "EndYear"),model="within",effect="twoways")


p1way2 <- plm(ESG_Information_Disclosure ~ after_first_inspection + ESG_Rate  + RegisterCapital_log + ROA + Leverage + as.factor(IndustryName)+ as.factor(PROVINCE) + ESG_Rate, data=dta2,
              index=c("Symbol"),model="within")

p1way3 <- plm(ESG_Information_Disclosure ~ Age + after_first_inspection * connections_dummy + as.factor(IndustryName)+ as.factor(PROVINCE) + ESG_Rate + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta2,
              index=c("Symbol"),model="within")

library(stargazer)
stargazer(p1way1, p1way2, p1way3,
          type="text", 
          column.labels = c("Year", "Individual", "Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)

```


### P2 central/local connection (dummy)
```{r P2 central/local connection (dummy)}
#p2

library(plm)

p2m2 <- plm(ESG_Information_Disclosure ~ Age + central_connection_dummy * after_first_inspection + ESG_Rate + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

p2m1 <- plm(ESG_Information_Disclosure ~ Age + central_connection_dummy + ESG_Rate + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")

p2m4 <- plm(ESG_Information_Disclosure ~ Age + local_connection_dummy * after_first_inspection+ ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

p2m3 <- plm(ESG_Information_Disclosure ~ Age + local_connection_dummy + ESG_Rate + RegisterCapital_log + ROA + as.factor(IndustryName)+ as.factor(PROVINCE) + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")


library(stargazer)
stargazer(p2m1, p2m2, p2m3,p2m4,
          type="text", 
          column.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```
### P3 connection (continuous)
```{r P3 connection (continuous)}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(plm)
p3way1 <- plm(ESG_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ESG_Rate, data=dta2,
              index=c("Symbol", "EndYear"),model="within",effect="twoways")


p3way2 <- plm(ESG_Information_Disclosure ~ after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
              index=c("Symbol"),model="within")

p3way3 <- plm(ESG_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta2,
              index=c("Symbol"),model="within")

library(stargazer)
stargazer(p3way1, p3way2, p3way3,
          type="text", 
          column.labels = c("Year", "Individual", "Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


```

### P4 contral/local connection (continuous)
```{r P4 contral/local connection (continuous)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p4m1 <- plm(ESG_Information_Disclosure ~ Age + central_connection + ESG_Rate  + RegisterCapital_log + as.factor(IndustryName)+ as.factor(PROVINCE) + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")

p4m2 <- plm(ESG_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

p4m3 <- plm(ESG_Information_Disclosure ~ Age + local_connection + ESG_Rate  + RegisterCapital_log + as.factor(IndustryName)+ as.factor(PROVINCE) + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")

p4m4 <- plm(ESG_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

library(stargazer)
stargazer(p4m1, p4m2, p4m3,p4m4,
          type="text", 
          column.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```


### P5 contral + local connection (continuous)
```{r P5 contral + local connection (continuous)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p5m1 <- plm(ESG_Information_Disclosure ~ Age + central_connection + local_connection + ESG_Rate  + RegisterCapital_log + as.factor(IndustryName)+ as.factor(PROVINCE) + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")

p5m2 <- plm(ESG_Information_Disclosure ~ central_connection * after_first_inspection + local_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

p5m3 <- plm(ESG_Information_Disclosure ~ Age + connection_num + ESG_Rate  + RegisterCapital_log + as.factor(IndustryName)+ as.factor(PROVINCE) + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")

p5m4 <- plm(ESG_Information_Disclosure ~ connection_num * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

library(stargazer)
stargazer(p5m1, p5m2, p5m3,p5m4,
          type="html", 
          out = "p5.html",
          column.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```


### final version
```{r}


library(plm)
p3way3 <- plm(ESG_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta2,
              index=c("Symbol"),model="within")

p4m2 <- plm(ESG_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")
p4m4 <- plm(ESG_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

library(stargazer)
stargazer(p3way3, p4m2,p4m4,
          type="text", 
          column.labels = c("Individual", "Central: Individual", "Local: Individual"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```
```{r}
library(plm) 
p3way3 <- plm(ESG_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta2,
              index=c("Symbol"),model="within")

p4m2 <- plm(ESG_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")
p4m4 <- plm(ESG_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta2,
            index=c("Symbol"),model="within")

# 使用stargazer生成表格，但设置type="html"以便输出HTML格式
library(stargazer) 
stargazer(p3way3, p4m2, p4m4, 
          type="html", 
          omit="as.factor",
          column.labels = c("(1)", "(2)", "(3)"), 
          notes="***p < 0.001; **p < 0.01; *p < 0.05",
          out="d:/Research/ESG/ESG_China/R-ESG China/RR_fixed_effects_table.html"
)

```

```{r}
# Script to count the number of years and observations in the dataset

# Load required libraries
library(plm)
dta2 = dta1
# Assuming dta2 is already loaded in your environment
# If not, you would need to load it first

# Count unique years
if(exists("dta2")) {
  # Check if there's a year column directly
  if("EndYear" %in% colnames(dta2)) {
    num_years <- length(unique(dta2$EndYear))
    years_list <- unique(dta2$EndYear)
  } else if("EndYear" %in% colnames(dta2)) {
    num_years <- length(unique(dta2$EndYear))
    years_list <- unique(dta2$EndYear)
  } else {
    # Try to extract year information from the index if it's a panel dataset
    pdim <- pdim(dta2)
    if(!is.null(pdim$balanced) && "time" %in% names(pdim$balanced)) {
      num_years <- length(unique(pdim$balanced$time))
      years_list <- unique(pdim$balanced$time)
    } else {
      num_years <- "Cannot determine (year column not found)"
      years_list <- NULL
    }
  }
  
  # Count total observations
  num_obs <- nrow(dta2)
  
  # Print results
  cat("Number of years:", num_years, "\n")
  if(!is.null(years_list)) {
    cat("Years included:", paste(sort(years_list), collapse=", "), "\n")
  }
  cat("Total number of observations:", num_obs, "\n")
  
  # Additional information - number of unique firms/entities
  if("Symbol" %in% colnames(dta2)) {
    num_firms <- length(unique(dta2$Symbol))
    cat("Number of unique firms/entities:", num_firms, "\n")
  }
} else {
  cat("Error: The dataset 'dta2' is not found in the environment.\n")
  cat("Please make sure to load the dataset first.\n")
}
```


```{r}
# Analysis of observations and years in the panel data models

# Load necessary libraries
library(plm)

# Function to analyze model data
analyze_model <- function(model_name, model) {
  cat("\n=== Analysis for", model_name, "===\n")
  
  # Get model information
  model_obs <- nobs(model)
  model_df <- df.residual(model)
  
  # Extract data information
  model_data <- model$model
  entities <- length(unique(index(model)[[1]]))
  
  cat("Observations used in model:", model_obs, "\n")
  cat("Residual degrees of freedom:", model_df, "\n")
  cat("Number of entities (firms):", entities, "\n")
  
  # Try to get time information
  if (length(index(model)) > 1) {
    times <- unique(index(model)[[2]])
    cat("Number of time periods:", length(times), "\n")
    cat("Time periods:", paste(sort(times), collapse=", "), "\n")
  } else {
    # For models with only one index (Symbol)
    if ("EndYear" %in% names(model_data)) {
      times <- unique(model_data$EndYear)
      cat("Number of time periods:", length(times), "\n")
      cat("Time periods:", paste(sort(times), collapse=", "), "\n")
    } else if ("after_first_inspection" %in% names(model_data)) {
      cat("Model uses after_first_inspection rather than explicit years\n")
    }
  }
}

# First, check the original data structure
cat("=== Original Data Structure (dta1) ===\n")
cat("Total observations in dta1:", nrow(dta1), "\n")

if ("EndYear" %in% names(dta1)) {
  years <- unique(dta1$EndYear)
  cat("Total unique years in data:", length(years), "\n")
  cat("Years included:", paste(sort(years), collapse=", "), "\n")
}

cat("Number of unique firms:", length(unique(dta1$Symbol)), "\n\n")

# Print panel dimensions
cat("Panel data structure:\n")
print(pdim(dta1))

# Run the original models to analyze
p3way1 <- plm(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + 
                Leverage + as.factor(IndustryName) + as.factor(PROVINCE) + 
                RegisterCapital_log + ESG_Rate, 
              data=dta1, index=c("Symbol", "EndYear"), model="within", effect="twoways")

p3way2 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + 
                as.factor(IndustryName) + as.factor(PROVINCE) + 
                RegisterCapital_log + ROA + Leverage + ESG_Rate, 
              data=dta1, index=c("Symbol"), model="within")

p3way3 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
                RegisterCapital_log + ROA + Leverage + ESG_Rate, 
              data=dta1, index=c("Symbol"), model="within")

# Analyze each model
analyze_model("p3way1 (Year + Individual FE)", p3way1)
analyze_model("p3way2 (Individual FE only)", p3way2)
analyze_model("p3way3 (Individual FE with interaction)", p3way3)

# Print model summary for more detailed information
cat("\n=== Summary of observations used in each model ===\n")
cat("p3way1:", nobs(p3way1), "observations\n")
cat("p3way2:", nobs(p3way2), "observations\n")
cat("p3way3:", nobs(p3way3), "observations\n")

```

