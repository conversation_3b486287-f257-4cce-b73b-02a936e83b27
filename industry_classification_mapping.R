# 行业分类映射分析
# 将70多个行业分类映射到11类和13类，输出每类对应的公司数量分布

# 加载必要的库
library(dplyr)
library(knitr)
library(kableExtra)

# 加载数据
load("dta1_20240903.RData")

# 检查原始数据结构
cat("=== 原始数据概览 ===\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")

# 显示原始行业列表
cat("\n=== 原始行业列表 ===\n")
original_industries <- sort(unique(dta1$IndustryName))
for(i in 1:length(original_industries)) {
  cat(sprintf("%2d. %s\n", i, original_industries[i]))
}

# 创建11类行业分类
cat("\n=== 创建11类行业分类 ===\n")
dta1$industry_type11 <- case_when(
  # Energy
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "石油加工、炼焦及核燃料加工业", "石油和天然气开采业", 
                           "煤炭开采和洗选业") ~ "Energy",
  
  # Consumer Discretionary
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业", "住宿业", 
                           "餐饮业", "体育", 
                           "纺织服装、服饰业", "文教、工美、体育和娱乐用品制造业", 
                           "文化艺术业", "皮革、毛皮、羽毛及其制品和制鞋业", 
                           "家具制造业", "机动车、电子产品和日用产品修理业") ~ "Consumer Discretionary",
  
  # Real Estate
  dta1$IndustryName %in% c("房地产业", "房屋建筑业", 
                           "建筑安装业", "土木工程建筑业", 
                           "建筑装饰和其他建筑业") ~ "Real Estate",
  
  # Utilities
  dta1$IndustryName %in% c("水的生产和供应业", "公共设施管理业", 
                           "生态保护和环境治理业") ~ "Utilities",
  
  # Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "互联网和相关服务", 
                           "邮政业") ~ "Communication Services",
  
  # Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "计算机、通信和其他电子设备制造业", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业", "专用设备制造业", 
                           "通用设备制造业", "仪器仪表制造业", 
                           "装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "航空运输业", 
                           "铁路运输业", "仓储业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "金属制品业", "其他制造业", 
                           "橡胶和塑料制品业", "木材加工及木、竹、藤、棕、草制品业", 
                           "废弃资源综合利用业", "金属制品、机械和设备修理业") ~ "Industrials",
  
  # Consumer Staples
  dta1$IndustryName %in% c("食品制造业", "农副食品加工业", 
                           "畜牧业", "渔业", 
                           "农业", "林业", 
                           "农、林、牧、渔服务业") ~ "Consumer Staples",
  
  # Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业", "化学纤维制造业", 
                           "非金属矿物制品业", "造纸及纸制品业", 
                           "印刷和记录媒介复制业") ~ "Materials",
  
  # Health Care
  dta1$IndustryName %in% c("医药制造业", "卫生") ~ "Health Care",
  
  # Financials
  dta1$IndustryName %in% c("货币金融服务", "商务服务业", 
                           "资本市场服务", "其他金融业", 
                           "保险业", "租赁业") ~ "Financials"
)

# 创建13类行业分类
cat("=== 创建13类行业分类 ===\n")
dta1$industry13 <- case_when(
  # 1. Agriculture, Forestry, Livestock Farming, Fishery
  dta1$IndustryName %in% c("农业", "林业", "畜牧业", "渔业", 
                           "农、林、牧、渔服务业") ~ "Agriculture, Forestry, Livestock Farming, Fishery",
  
  # 2. Mining
  dta1$IndustryName %in% c("煤炭开采和洗选业", "石油和天然气开采业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "开采辅助活动") ~ "Mining",
  
  # 3. Manufacturing
  dta1$IndustryName %in% c("医药制造业", "化学原料及化学制品制造业", 
                           "化学纤维制造业", "非金属矿物制品业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "金属制品业", "通用设备制造业", 
                           "专用设备制造业", "铁路、船舶、航空航天和其它运输设备制造业", 
                           "汽车制造业", "电气机械及器材制造业", 
                           "计算机、通信和其他电子设备制造业", "仪器仪表制造业", 
                           "其他制造业", "食品制造业", 
                           "酒、饮料和精制茶制造业", "农副食品加工业", 
                           "纺织业", "纺织服装、服饰业", 
                           "皮革、毛皮、羽毛及其制品和制鞋业", "木材加工及木、竹、藤、棕、草制品业", 
                           "造纸及纸制品业", "印刷和记录媒介复制业", 
                           "橡胶和塑料制品业", "家具制造业", 
                           "废弃资源综合利用业", "文教、工美、体育和娱乐用品制造业", 
                           "金属制品、机械和设备修理业", "石油加工、炼焦及核燃料加工业") ~ "Manufacturing",
  
  # 4. Electric Power, Gas, and Water Production and Supply
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "水的生产和供应业") ~ "Electric Power, Gas, and Water Production and Supply",
  
  # 5. Construction
  dta1$IndustryName %in% c("房屋建筑业", "建筑安装业", 
                           "土木工程建筑业", "建筑装饰和其他建筑业") ~ "Construction",
  
  # 6. Transport and Storage
  dta1$IndustryName %in% c("装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "铁路运输业", 
                           "航空运输业", "仓储业") ~ "Transport and Storage",
  
  # 7. Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "互联网和相关服务", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # 8. Wholesale and Retail Trade
  dta1$IndustryName %in% c("批发业", "零售业") ~ "Wholesale and Retail Trade",
  
  # 9. Finance and Insurance
  dta1$IndustryName %in% c("货币金融服务", "资本市场服务", 
                           "其他金融业", "保险业", 
                           "租赁业") ~ "Finance and Insurance",
  
  # 10. Real Estate
  dta1$IndustryName %in% c("房地产业") ~ "Real Estate",
  
  # 11. Social Service
  dta1$IndustryName %in% c("教育", "卫生", 
                           "公共设施管理业", "生态保护和环境治理业", 
                           "居民服务业") ~ "Social Service",
  
  # 12. Communication and Culture
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "文化艺术业", 
                           "体育", "邮政业") ~ "Communication and Culture",
  
  # 13. Others
  TRUE ~ "Others"
)

# 统计11类行业分类的公司数量分布
cat("\n=== 11类行业分类公司数量分布 ===\n")

# 包含NA的情况
cat("\n--- 包含NA的情况 ---\n")
industry11_count_with_na <- dta1 %>%
  group_by(industry_type11) %>%
  summarise(
    company_count = n_distinct(Symbol),
    observation_count = n(),
    .groups = 'drop'
  ) %>%
  arrange(desc(company_count))

print(kable(industry11_count_with_na, 
            col.names = c("行业分类(11类)", "公司数量", "观测数量"),
            caption = "11类行业分类公司数量分布(包含NA)"))

# 不包含NA的情况
cat("\n--- 不包含NA的情况 ---\n")
industry11_count_no_na <- dta1 %>%
  filter(!is.na(industry_type11)) %>%
  group_by(industry_type11) %>%
  summarise(
    company_count = n_distinct(Symbol),
    observation_count = n(),
    .groups = 'drop'
  ) %>%
  arrange(desc(company_count))

print(kable(industry11_count_no_na, 
            col.names = c("行业分类(11类)", "公司数量", "观测数量"),
            caption = "11类行业分类公司数量分布(不含NA)"))

# 统计13类行业分类的公司数量分布
cat("\n=== 13类行业分类公司数量分布 ===\n")

# 包含NA的情况
cat("\n--- 包含NA的情况 ---\n")
industry13_count_with_na <- dta1 %>%
  group_by(industry13) %>%
  summarise(
    company_count = n_distinct(Symbol),
    observation_count = n(),
    .groups = 'drop'
  ) %>%
  arrange(desc(company_count))

print(kable(industry13_count_with_na, 
            col.names = c("行业分类(13类)", "公司数量", "观测数量"),
            caption = "13类行业分类公司数量分布(包含NA)"))

# 不包含NA的情况
cat("\n--- 不包含NA的情况 ---\n")
industry13_count_no_na <- dta1 %>%
  filter(!is.na(industry13)) %>%
  group_by(industry13) %>%
  summarise(
    company_count = n_distinct(Symbol),
    observation_count = n(),
    .groups = 'drop'
  ) %>%
  arrange(desc(company_count))

print(kable(industry13_count_no_na, 
            col.names = c("行业分类(13类)", "公司数量", "观测数量"),
            caption = "13类行业分类公司数量分布(不含NA)"))

# 汇总统计
cat("\n=== 汇总统计 ===\n")
cat("原始行业数量:", length(unique(dta1$IndustryName)), "\n")
cat("11类分类中有效分类数:", sum(!is.na(dta1$industry_type11)), "\n")
cat("11类分类中NA数量:", sum(is.na(dta1$industry_type11)), "\n")
cat("13类分类中有效分类数:", sum(!is.na(dta1$industry13)), "\n")
cat("13类分类中NA数量:", sum(is.na(dta1$industry13)), "\n")

# 创建11类分类映射表
cat("\n=== 创建11类分类映射表 ===\n")
industry11_mapping <- dta1 %>%
  select(IndustryName, industry_type11) %>%
  distinct() %>%
  arrange(industry_type11, IndustryName)

print(kable(industry11_mapping,
            col.names = c("原始行业名称", "11类分类"),
            caption = "原始行业到11类分类的映射表"))

# 创建13类分类映射表
cat("\n=== 创建13类分类映射表 ===\n")
industry13_mapping <- dta1 %>%
  select(IndustryName, industry13) %>%
  distinct() %>%
  arrange(industry13, IndustryName)

print(kable(industry13_mapping,
            col.names = c("原始行业名称", "13类分类"),
            caption = "原始行业到13类分类的映射表"))

# 保存结果到文件
write.csv(industry11_count_with_na, "industry11_distribution_with_na.csv", row.names = FALSE)
write.csv(industry11_count_no_na, "industry11_distribution_no_na.csv", row.names = FALSE)
write.csv(industry13_count_with_na, "industry13_distribution_with_na.csv", row.names = FALSE)
write.csv(industry13_count_no_na, "industry13_distribution_no_na.csv", row.names = FALSE)

# 保存映射表
write.csv(industry11_mapping, "industry11_mapping_table.csv", row.names = FALSE)
write.csv(industry13_mapping, "industry13_mapping_table.csv", row.names = FALSE)

cat("\n结果已保存到CSV文件中。\n")
cat("映射表已保存为: industry11_mapping_table.csv 和 industry13_mapping_table.csv\n")
