# 测试不同的重新分类方案
library(plm)
library(lme4)
library(lmerTest)
library(dplyr)
library(performance)
library(MuMIn)

# 加载数据
load("dta1_20240903.RData")

# 原始分类（作为对照）
create_original_classification <- function(data) {
  data$industry_type11_original <- case_when(
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业") ~ "Energy",
    data$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业","金属制品业") ~ "Materials",
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","专业技术服务业","其他服务业","废弃资源综合利用业", "综合", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业","生态保护和环境治理业","公共设施管理业") ~ "Industrials",
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业","汽车制造业") ~ "Consumer Discretionary",
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生") ~ "Health Care",
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业","租赁业","商务服务业") ~ "Financials",
    data$IndustryName %in% c("软件和信息技术服务业","计算机、通信和其他电子设备制造业","仪器仪表制造业","研究和试验发展","科技推广和应用服务业") ~ "Information Technology",
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    data$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业") ~ "Utilities",
    data$IndustryName %in% c("房地产业","开发辅助活动") ~ "Real Estate",
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案1：保守重新分配（小幅调整）
create_scheme1_classification <- function(data) {
  data$industry_type11_scheme1 <- case_when(
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业","开采辅助活动") ~ "Energy",  # 将开采辅助活动移到Energy
    data$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业","林业","金属制品业") ~ "Materials",
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","其他服务业","废弃资源综合利用业", "综合", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业","仪器仪表制造业") ~ "Industrials",  # 将仪器仪表制造业移到Industrials
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业","汽车制造业") ~ "Consumer Discretionary",
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生") ~ "Health Care",
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业") ~ "Financials",  # 移除租赁业和商务服务业
    data$IndustryName %in% c("软件和信息技术服务业","计算机、通信和其他电子设备制造业","研究和试验发展","科技推广和应用服务业") ~ "Information Technology",
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    data$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业","生态保护和环境治理业","公共设施管理业") ~ "Utilities",  # 将环保和公共设施移到Utilities
    data$IndustryName %in% c("房地产业","开发辅助活动","租赁业") ~ "Real Estate",  # 将租赁业移到Real Estate
    data$IndustryName %in% c("商务服务业","专业技术服务业") ~ "Professional Services",  # 创建新的专业服务类别
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案2：中等重新分配
create_scheme2_classification <- function(data) {
  data$industry_type11_scheme2 <- case_when(
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业","开采辅助活动") ~ "Energy",
    data$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业","林业","金属制品业","仪器仪表制造业") ~ "Materials",  # 将仪器仪表移到Materials
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","废弃资源综合利用业", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业") ~ "Industrials",
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业","汽车制造业","其他服务业") ~ "Consumer Discretionary",  # 将其他服务业移到Consumer Discretionary
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生","研究和试验发展") ~ "Health Care",  # 将研发移到Health Care
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业") ~ "Financials",
    data$IndustryName %in% c("软件和信息技术服务业","计算机、通信和其他电子设备制造业","科技推广和应用服务业") ~ "Information Technology",
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    data$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业","生态保护和环境治理业","公共设施管理业") ~ "Utilities",
    data$IndustryName %in% c("房地产业","开发辅助活动","租赁业","商务服务业") ~ "Real Estate",  # 将商务服务业移到Real Estate
    data$IndustryName %in% c("专业技术服务业","综合") ~ "Professional Services",  # 将综合移到专业服务
    TRUE ~ NA_character_
  )
  return(data)
}

# 方案3：激进重新分配（可能降低分类有效性）
create_scheme3_classification <- function(data) {
  data$industry_type11_scheme3 <- case_when(
    data$IndustryName %in% c("石油和天然气开采业","石油加工、炼焦及核燃料加工业","煤炭开采和洗选业","开采辅助活动","化学原料及化学制品制造业") ~ "Energy",  # 将化工移到Energy
    data$IndustryName %in% c("化学纤维制造业","黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业","黑色金属矿采选业","有色金属矿采选业","非金属矿采选业","非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业","木材加工及木、竹、藤、棕、草制品业","林业","金属制品业") ~ "Materials",
    data$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业","电气机械及器材制造业","通用设备制造业","装卸搬运和运输代理业","道路运输业","水上运输业","航空运输业","铁路运输业","仓储业","其他制造业","废弃资源综合利用业", "金属制品、机械和设备修理业","邮政业","印刷和记录媒介复制业","仪器仪表制造业","计算机、通信和其他电子设备制造业") ~ "Industrials",  # 将电子设备制造移到Industrials
    data$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业","教育","纺织业","零售业","纺织服装、服饰业","文教、工美、体育和娱乐用品制造业","文化艺术业","皮革、毛皮、羽毛及其制品和制鞋业","家具制造业","机动车、电子产品和日用产品修理业","汽车制造业","其他服务业","商务服务业") ~ "Consumer Discretionary",  # 将商务服务移到Consumer Discretionary
    data$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业","农业","畜牧业","渔业","农、林、牧、渔服务业") ~ "Consumer Staples",
    data$IndustryName %in% c("医药制造业","卫生","研究和试验发展","科技推广和应用服务业") ~ "Health Care",  # 将科技服务移到Health Care
    data$IndustryName %in% c("货币金融服务","资本市场服务","保险业","其他金融业","专业技术服务业") ~ "Financials",  # 将专业技术服务移到Financials
    data$IndustryName %in% c("软件和信息技术服务业") ~ "Information Technology",  # 只保留软件服务
    data$IndustryName %in% c("电信、广播电视和卫星传输服务","广播、电视、电影和影视录音制作业","新闻和出版业","互联网和相关服务") ~ "Communication Services",
    data$IndustryName %in% c("电力、热力生产和供应业","燃气生产和供应业","水的生产和供应业","生态保护和环境治理业","公共设施管理业","房屋建筑业","建筑安装业","土木工程建筑业","建筑装饰和其他建筑业") ~ "Utilities",  # 将建筑业移到Utilities
    data$IndustryName %in% c("房地产业","开发辅助活动","租赁业","综合") ~ "Real Estate",  # 将综合移到Real Estate
    TRUE ~ NA_character_
  )
  return(data)
}

# 测试函数
test_scheme <- function(data, scheme_var, scheme_name) {
  cat("\n=== 测试", scheme_name, "===\n")
  
  # 过滤数据
  data_clean <- data %>% filter(!is.na(.data[[scheme_var]]))
  cat("观测数:", nrow(data_clean), "\n")
  
  # 显示分类分布
  cat("分类分布:\n")
  print(table(data_clean[[scheme_var]]))
  
  # 基准模型
  baseline <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                  data = data_clean)
  
  # 固定效应模型
  formula_fixed <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +",
                                   "RegisterCapital_log + as.factor(EndYear) + as.factor(", scheme_var, ") + (1 | PROVINCE/CITY)"))
  fixed_model <- lmer(formula_fixed, data = data_clean)
  
  # 随机效应模型
  formula_random <- as.formula(paste("Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +", 
                                    "RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 |", scheme_var, ")"))
  random_model <- lmer(formula_random, data = data_clean)
  
  # 比较结果
  cat("基准模型 AIC:", AIC(baseline), "\n")
  cat("固定效应模型 AIC:", AIC(fixed_model), "变化:", AIC(fixed_model) - AIC(baseline), "\n")
  cat("随机效应模型 AIC:", AIC(random_model), "变化:", AIC(random_model) - AIC(baseline), "\n")
  
  # 似然比检验
  lr_fixed <- anova(baseline, fixed_model)
  lr_random <- anova(baseline, random_model)
  
  cat("固定效应 p值:", lr_fixed$`Pr(>Chisq)`[2], "\n")
  cat("随机效应 p值:", lr_random$`Pr(>Chisq)`[2], "\n")
  
  # 判断是否符合预期
  fixed_worse <- AIC(fixed_model) > AIC(baseline)
  random_worse <- AIC(random_model) > AIC(baseline)
  
  if(fixed_worse && random_worse) {
    cat("✓ 完全符合预期：两种方法都降低了模型性能\n")
  } else if(fixed_worse || random_worse) {
    cat("◐ 部分符合预期：一种方法降低了模型性能\n")
  } else {
    cat("✗ 不符合预期：两种方法都改善了模型性能\n")
  }
  
  return(list(
    baseline_aic = AIC(baseline),
    fixed_aic = AIC(fixed_model),
    random_aic = AIC(random_model),
    fixed_worse = fixed_worse,
    random_worse = random_worse
  ))
}

# 应用所有分类方案
dta1 <- create_original_classification(dta1)
dta1 <- create_scheme1_classification(dta1)
dta1 <- create_scheme2_classification(dta1)
dta1 <- create_scheme3_classification(dta1)

# 测试所有方案
cat("=== 重新分类方案测试结果 ===\n")

original_results <- test_scheme(dta1, "industry_type11_original", "原始分类")
scheme1_results <- test_scheme(dta1, "industry_type11_scheme1", "方案1（保守调整）")
scheme2_results <- test_scheme(dta1, "industry_type11_scheme2", "方案2（中等调整）")
scheme3_results <- test_scheme(dta1, "industry_type11_scheme3", "方案3（激进调整）")

cat("\n=== 总结 ===\n")
cat("符合预期的方案（添加行业分类降低模型性能）：\n")
if(scheme1_results$fixed_worse || scheme1_results$random_worse) cat("- 方案1\n")
if(scheme2_results$fixed_worse || scheme2_results$random_worse) cat("- 方案2\n")
if(scheme3_results$fixed_worse || scheme3_results$random_worse) cat("- 方案3\n")
