# 行业分类回归分析脚本
# 基于OE revision v2.rmd，使用11类和13类行业分类

# 加载必要的库
library(plm)
library(stargazer)
library(dplyr)

# 加载数据
load("dta1_20240903.RData")

cat("=== 数据概览 ===\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")

# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # Energy
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "石油加工、炼焦及核燃料加工业", "石油和天然气开采业", 
                           "煤炭开采和洗选业") ~ "Energy",
  
  # Consumer Discretionary
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业", "住宿业", 
                           "餐饮业", "体育", 
                           "纺织服装、服饰业", "文教、工美、体育和娱乐用品制造业", 
                           "文化艺术业", "皮革、毛皮、羽毛及其制品和制鞋业", 
                           "家具制造业", "机动车、电子产品和日用产品修理业") ~ "Consumer Discretionary",
  
  # Real Estate
  dta1$IndustryName %in% c("房地产业", "房屋建筑业", 
                           "建筑安装业", "土木工程建筑业", 
                           "建筑装饰和其他建筑业") ~ "Real Estate",
  
  # Utilities
  dta1$IndustryName %in% c("水的生产和供应业", "公共设施管理业", 
                           "生态保护和环境治理业") ~ "Utilities",
  
  # Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "互联网和相关服务", 
                           "邮政业") ~ "Communication Services",
  
  # Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "计算机、通信和其他电子设备制造业", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业", "专用设备制造业", 
                           "通用设备制造业", "仪器仪表制造业", 
                           "装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "航空运输业", 
                           "铁路运输业", "仓储业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "金属制品业", "其他制造业", 
                           "橡胶和塑料制品业", "木材加工及木、竹、藤、棕、草制品业", 
                           "废弃资源综合利用业", "金属制品、机械和设备修理业") ~ "Industrials",
  
  # Consumer Staples
  dta1$IndustryName %in% c("食品制造业", "农副食品加工业", 
                           "畜牧业", "渔业", 
                           "农业", "林业", 
                           "农、林、牧、渔服务业") ~ "Consumer Staples",
  
  # Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业", "化学纤维制造业", 
                           "非金属矿物制品业", "造纸及纸制品业", 
                           "印刷和记录媒介复制业") ~ "Materials",
  
  # Health Care
  dta1$IndustryName %in% c("医药制造业", "卫生") ~ "Health Care",
  
  # Financials
  dta1$IndustryName %in% c("货币金融服务", "商务服务业", 
                           "资本市场服务", "其他金融业", 
                           "保险业", "租赁业") ~ "Financials"
)

# 创建13类行业分类
dta1$industry13 <- case_when(
  # 1. Agriculture, Forestry, Livestock Farming, Fishery
  dta1$IndustryName %in% c("农业", "林业", "畜牧业", "渔业", 
                           "农、林、牧、渔服务业") ~ "Agriculture, Forestry, Livestock Farming, Fishery",
  
  # 2. Mining
  dta1$IndustryName %in% c("煤炭开采和洗选业", "石油和天然气开采业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "开采辅助活动") ~ "Mining",
  
  # 3. Manufacturing
  dta1$IndustryName %in% c("医药制造业", "化学原料及化学制品制造业", 
                           "化学纤维制造业", "非金属矿物制品业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "金属制品业", "通用设备制造业", 
                           "专用设备制造业", "铁路、船舶、航空航天和其它运输设备制造业", 
                           "汽车制造业", "电气机械及器材制造业", 
                           "计算机、通信和其他电子设备制造业", "仪器仪表制造业", 
                           "其他制造业", "食品制造业", 
                           "酒、饮料和精制茶制造业", "农副食品加工业", 
                           "纺织业", "纺织服装、服饰业", 
                           "皮革、毛皮、羽毛及其制品和制鞋业", "木材加工及木、竹、藤、棕、草制品业", 
                           "造纸及纸制品业", "印刷和记录媒介复制业", 
                           "橡胶和塑料制品业", "家具制造业", 
                           "废弃资源综合利用业", "文教、工美、体育和娱乐用品制造业", 
                           "金属制品、机械和设备修理业", "石油加工、炼焦及核燃料加工业") ~ "Manufacturing",
  
  # 4. Electric Power, Gas, and Water Production and Supply
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "水的生产和供应业") ~ "Electric Power, Gas, and Water Production and Supply",
  
  # 5. Construction
  dta1$IndustryName %in% c("房屋建筑业", "建筑安装业", 
                           "土木工程建筑业", "建筑装饰和其他建筑业") ~ "Construction",
  
  # 6. Transport and Storage
  dta1$IndustryName %in% c("装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "铁路运输业", 
                           "航空运输业", "仓储业") ~ "Transport and Storage",
  
  # 7. Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "互联网和相关服务", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # 8. Wholesale and Retail Trade
  dta1$IndustryName %in% c("批发业", "零售业") ~ "Wholesale and Retail Trade",
  
  # 9. Finance and Insurance
  dta1$IndustryName %in% c("货币金融服务", "资本市场服务", 
                           "其他金融业", "保险业", 
                           "租赁业") ~ "Finance and Insurance",
  
  # 10. Real Estate
  dta1$IndustryName %in% c("房地产业") ~ "Real Estate",
  
  # 11. Social Service
  dta1$IndustryName %in% c("教育", "卫生", 
                           "公共设施管理业", "生态保护和环境治理业", 
                           "居民服务业") ~ "Social Service",
  
  # 12. Communication and Culture
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "文化艺术业", 
                           "体育", "邮政业") ~ "Communication and Culture",
  
  # 13. Others
  TRUE ~ "Others"
)

cat("\n=== 行业分类结果 ===\n")
cat("11类分类中NA数量:", sum(is.na(dta1$industry_type11)), "\n")
cat("13类分类中NA数量:", sum(is.na(dta1$industry13)), "\n")

cat("\n=== 开始运行11类分类模型 ===\n")

# P3 模型 - 使用11类行业分类
p3way3_11 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                 ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

# P4 模型 - 使用11类行业分类
p4m2_11 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m4_11 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

cat("11类分类模型完成\n")

cat("\n=== 开始运行13类分类模型 ===\n")

# P3 模型 - 使用13类行业分类
p3way3_13 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                 ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

# P4 模型 - 使用13类行业分类
p4m2_13 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m4_13 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

cat("13类分类模型完成\n")

cat("\n=== 输出11类分类模型结果 ===\n")
stargazer(p3way3_11, p4m2_11, p4m4_11,
          type="text", 
          column.labels = c("P3 (总关联)", "P4 (中央关联)", "P4 (地方关联)"),
          title="使用11类行业分类的回归结果",
          omit="as.factor",
          notes=c("行业固定效应 (11类) 和省份固定效应已包含"),
          dep.var.labels = "环境信息披露")

cat("\n=== 输出13类分类模型结果 ===\n")
stargazer(p3way3_13, p4m2_13, p4m4_13,
          type="text", 
          column.labels = c("P3 (总关联)", "P4 (中央关联)", "P4 (地方关联)"),
          title="使用13类行业分类的回归结果",
          omit="as.factor",
          notes=c("行业固定效应 (13类) 和省份固定效应已包含"),
          dep.var.labels = "环境信息披露")

# 模型比较
cat("\n=== 模型比较 ===\n")
comparison_data <- data.frame(
  Model = c("P3 (总关联)", "P4 (中央关联)", "P4 (地方关联)"),
  R2_11class = c(summary(p3way3_11)$r.squared[1], 
                 summary(p4m2_11)$r.squared[1], 
                 summary(p4m4_11)$r.squared[1]),
  Obs_11class = c(nobs(p3way3_11), nobs(p4m2_11), nobs(p4m4_11)),
  R2_13class = c(summary(p3way3_13)$r.squared[1], 
                 summary(p4m2_13)$r.squared[1], 
                 summary(p4m4_13)$r.squared[1]),
  Obs_13class = c(nobs(p3way3_13), nobs(p4m2_13), nobs(p4m4_13))
)

comparison_data$R2_11class <- round(comparison_data$R2_11class, 4)
comparison_data$R2_13class <- round(comparison_data$R2_13class, 4)

print(comparison_data)

cat("\n平均R²改进 (13类 vs 11类):", 
    round(mean(comparison_data$R2_13class - comparison_data$R2_11class), 4), "\n")
cat("平均观测数增加 (13类 vs 11类):", 
    round(mean(comparison_data$Obs_13class - comparison_data$Obs_11class), 0), "\n")

# 保存结果到HTML文件
stargazer(p3way3_11, p4m2_11, p4m4_11, p3way3_13, p4m2_13, p4m4_13,
          type="html", 
          out="industry_classification_results.html",
          column.labels = c("P3-11类", "P4中央-11类", "P4地方-11类", 
                           "P3-13类", "P4中央-13类", "P4地方-13类"),
          title="行业分类回归结果比较",
          omit="as.factor",
          notes=c("*** p<0.01, ** p<0.05, * p<0.1", 
                  "省份和行业固定效应已包含"),
          dep.var.labels = "环境信息披露")

cat("\n结果已保存到 industry_classification_results.html\n")
