---
title: "行业分类分析：11类与13类分类的回归模型比较"
author: "qcm
"
date: "`r Sys.Date()`"
format: 
  html:
    toc: true
    toc-depth: 3
    code-fold: false
    theme: cosmo
editor: visual
---

# 数据准备

## 加载必要的库和数据

```{r setup, message=FALSE, warning=FALSE}
# 加载必要的库
library(plm)
library(stargazer)
library(dplyr)
library(knitr)
library(kableExtra)

# 加载数据
load("dta1_20240903.RData")

# 检查数据结构
cat("原始数据概览:\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")
```

## 创建行业分类

```{r industry_classification}
# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # Energy
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "石油加工、炼焦及核燃料加工业", "石油和天然气开采业", 
                           "煤炭开采和洗选业") ~ "Energy",
  
  # Consumer Discretionary
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业", "住宿业", 
                           "餐饮业", "体育", 
                           "纺织服装、服饰业", "文教、工美、体育和娱乐用品制造业", 
                           "文化艺术业", "皮革、毛皮、羽毛及其制品和制鞋业", 
                           "家具制造业", "机动车、电子产品和日用产品修理业") ~ "Consumer Discretionary",
  
  # Real Estate
  dta1$IndustryName %in% c("房地产业", "房屋建筑业", 
                           "建筑安装业", "土木工程建筑业", 
                           "建筑装饰和其他建筑业") ~ "Real Estate",
  
  # Utilities
  dta1$IndustryName %in% c("水的生产和供应业", "公共设施管理业", 
                           "生态保护和环境治理业") ~ "Utilities",
  
  # Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "互联网和相关服务", 
                           "邮政业") ~ "Communication Services",
  
  # Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "计算机、通信和其他电子设备制造业", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业", "专用设备制造业", 
                           "通用设备制造业", "仪器仪表制造业", 
                           "装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "航空运输业", 
                           "铁路运输业", "仓储业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "金属制品业", "其他制造业", 
                           "橡胶和塑料制品业", "木材加工及木、竹、藤、棕、草制品业", 
                           "废弃资源综合利用业", "金属制品、机械和设备修理业") ~ "Industrials",
  
  # Consumer Staples
  dta1$IndustryName %in% c("食品制造业", "农副食品加工业", 
                           "畜牧业", "渔业", 
                           "农业", "林业", 
                           "农、林、牧、渔服务业") ~ "Consumer Staples",
  
  # Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业", "化学纤维制造业", 
                           "非金属矿物制品业", "造纸及纸制品业", 
                           "印刷和记录媒介复制业") ~ "Materials",
  
  # Health Care
  dta1$IndustryName %in% c("医药制造业", "卫生") ~ "Health Care",
  
  # Financials
  dta1$IndustryName %in% c("货币金融服务", "商务服务业", 
                           "资本市场服务", "其他金融业", 
                           "保险业", "租赁业") ~ "Financials"
)

# 创建13类行业分类
dta1$industry13 <- case_when(
  # 1. Agriculture, Forestry, Livestock Farming, Fishery
  dta1$IndustryName %in% c("农业", "林业", "畜牧业", "渔业", 
                           "农、林、牧、渔服务业") ~ "Agriculture, Forestry, Livestock Farming, Fishery",
  
  # 2. Mining
  dta1$IndustryName %in% c("煤炭开采和洗选业", "石油和天然气开采业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "开采辅助活动") ~ "Mining",
  
  # 3. Manufacturing
  dta1$IndustryName %in% c("医药制造业", "化学原料及化学制品制造业", 
                           "化学纤维制造业", "非金属矿物制品业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "金属制品业", "通用设备制造业", 
                           "专用设备制造业", "铁路、船舶、航空航天和其它运输设备制造业", 
                           "汽车制造业", "电气机械及器材制造业", 
                           "计算机、通信和其他电子设备制造业", "仪器仪表制造业", 
                           "其他制造业", "食品制造业", 
                           "酒、饮料和精制茶制造业", "农副食品加工业", 
                           "纺织业", "纺织服装、服饰业", 
                           "皮革、毛皮、羽毛及其制品和制鞋业", "木材加工及木、竹、藤、棕、草制品业", 
                           "造纸及纸制品业", "印刷和记录媒介复制业", 
                           "橡胶和塑料制品业", "家具制造业", 
                           "废弃资源综合利用业", "文教、工美、体育和娱乐用品制造业", 
                           "金属制品、机械和设备修理业", "石油加工、炼焦及核燃料加工业") ~ "Manufacturing",
  
  # 4. Electric Power, Gas, and Water Production and Supply
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "水的生产和供应业") ~ "Electric Power, Gas, and Water Production and Supply",
  
  # 5. Construction
  dta1$IndustryName %in% c("房屋建筑业", "建筑安装业", 
                           "土木工程建筑业", "建筑装饰和其他建筑业") ~ "Construction",
  
  # 6. Transport and Storage
  dta1$IndustryName %in% c("装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "铁路运输业", 
                           "航空运输业", "仓储业") ~ "Transport and Storage",
  
  # 7. Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "互联网和相关服务", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # 8. Wholesale and Retail Trade
  dta1$IndustryName %in% c("批发业", "零售业") ~ "Wholesale and Retail Trade",
  
  # 9. Finance and Insurance
  dta1$IndustryName %in% c("货币金融服务", "资本市场服务", 
                           "其他金融业", "保险业", 
                           "租赁业") ~ "Finance and Insurance",
  
  # 10. Real Estate
  dta1$IndustryName %in% c("房地产业") ~ "Real Estate",
  
  # 11. Social Service
  dta1$IndustryName %in% c("教育", "卫生", 
                           "公共设施管理业", "生态保护和环境治理业", 
                           "居民服务业") ~ "Social Service",
  
  # 12. Communication and Culture
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "文化艺术业", 
                           "体育", "邮政业") ~ "Communication and Culture",
  
  # 13. Others
  TRUE ~ "Others"
)

# 显示分类结果
cat("11类分类结果:\n")
table(dta1$industry_type11, useNA = "always")

cat("\n13类分类结果:\n")
table(dta1$industry13, useNA = "always")
```

# 使用11类行业分类的回归模型

## P3: 政治关联（连续变量）的影响

```{r p3_11class}
# P3 模型 - 使用11类行业分类
p3way1_11 <- plm(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + 
                 Leverage + as.factor(industry_type11) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

p3way2_11 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + 
                 as.factor(industry_type11) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

p3way3_11 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                 ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
                 data=dta1, index=c("EndYear"), model="within")

# 输出结果
stargazer(p3way1_11, p3way2_11, p3way3_11,
          type="text", 
          column.labels = c("Model 1", "Model 2", "Model 3"),
          title="P3 Models with 11-Class Industry Classification",
          omit="as.factor",
          notes=c("Industry fixed effects (11 classes) included"),
          dep.var.labels = "Environmental Information Disclosure")
```

## P4: 中央与地方政治关联（连续变量）的影响

```{r p4_11class}
# P4 模型 - 使用11类行业分类
p4m1_11 <- plm(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + 
               RegisterCapital_log + as.factor(industry_type11) + as.factor(PROVINCE) + 
               ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m2_11 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m3_11 <- plm(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + 
               RegisterCapital_log + as.factor(industry_type11) + as.factor(PROVINCE) + 
               ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

p4m4_11 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + 
               ESG_Rate + as.factor(industry_type11) + as.factor(PROVINCE) + 
               RegisterCapital_log + ROA + Leverage, 
               data=dta1, index=c("EndYear"), model="within")

# 输出结果
stargazer(p4m1_11, p4m2_11, p4m3_11, p4m4_11,
          type="text",
          column.labels = c("Central: Main", "Central: Interaction", "Local: Main", "Local: Interaction"),
          title="P4 Models with 11-Class Industry Classification",
          omit="as.factor",
          notes=c("Industry fixed effects (11 classes) included"),
          dep.var.labels = "Environmental Information Disclosure")
```

# 使用13类行业分类的回归模型

## P3: 政治关联（连续变量）的影响

```{r p3_13class}
# P3 模型 - 使用13类行业分类
p3way1_13 <- plm(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate +
                 Leverage + as.factor(industry13) + as.factor(PROVINCE) +
                 RegisterCapital_log + ESG_Rate,
                 data=dta1, index=c("EndYear"), model="within")

p3way2_13 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate +
                 as.factor(industry13) + as.factor(PROVINCE) +
                 RegisterCapital_log + ROA + Leverage + ESG_Rate,
                 data=dta1, index=c("EndYear"), model="within")

p3way3_13 <- plm(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                 ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) +
                 RegisterCapital_log + ROA + Leverage + ESG_Rate,
                 data=dta1, index=c("EndYear"), model="within")

# 输出结果
stargazer(p3way1_13, p3way2_13, p3way3_13,
          type="text",
          column.labels = c("Model 1", "Model 2", "Model 3"),
          title="P3 Models with 13-Class Industry Classification",
          omit="as.factor",
          notes=c("Industry fixed effects (13 classes) included"),
          dep.var.labels = "Environmental Information Disclosure")
```

## P4: 中央与地方政治关联（连续变量）的影响

```{r p4_13class}
# P4 模型 - 使用13类行业分类
p4m1_13 <- plm(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate +
               RegisterCapital_log + as.factor(industry13) + as.factor(PROVINCE) +
               ROA + Leverage,
               data=dta1, index=c("EndYear"), model="within")

p4m2_13 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
               ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) +
               RegisterCapital_log + ROA + Leverage,
               data=dta1, index=c("EndYear"), model="within")

p4m3_13 <- plm(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate +
               RegisterCapital_log + as.factor(industry13) + as.factor(PROVINCE) +
               ROA + Leverage,
               data=dta1, index=c("EndYear"), model="within")

p4m4_13 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
               ESG_Rate + as.factor(industry13) + as.factor(PROVINCE) +
               RegisterCapital_log + ROA + Leverage,
               data=dta1, index=c("EndYear"), model="within")

# 输出结果
stargazer(p4m1_13, p4m2_13, p4m3_13, p4m4_13,
          type="text",
          column.labels = c("Central: Main", "Central: Interaction", "Local: Main", "Local: Interaction"),
          title="P4 Models with 13-Class Industry Classification",
          omit="as.factor",
          notes=c("Industry fixed effects (13 classes) included"),
          dep.var.labels = "Environmental Information Disclosure")
```

# 模型比较与分析

## 模型拟合度比较

```{r model_comparison}
# 创建模型比较表
model_comparison <- data.frame(
  Model = c("P3 Model 1", "P3 Model 2", "P3 Model 3",
            "P4 Model 1", "P4 Model 2", "P4 Model 3", "P4 Model 4"),

  # 11类分类模型
  R2_11class = c(summary(p3way1_11)$r.squared[1],
                 summary(p3way2_11)$r.squared[1],
                 summary(p3way3_11)$r.squared[1],
                 summary(p4m1_11)$r.squared[1],
                 summary(p4m2_11)$r.squared[1],
                 summary(p4m3_11)$r.squared[1],
                 summary(p4m4_11)$r.squared[1]),

  # 观测数量
  Obs_11class = c(nobs(p3way1_11), nobs(p3way2_11), nobs(p3way3_11),
                  nobs(p4m1_11), nobs(p4m2_11), nobs(p4m3_11), nobs(p4m4_11)),

  # 13类分类模型
  R2_13class = c(summary(p3way1_13)$r.squared[1],
                 summary(p3way2_13)$r.squared[1],
                 summary(p3way3_13)$r.squared[1],
                 summary(p4m1_13)$r.squared[1],
                 summary(p4m2_13)$r.squared[1],
                 summary(p4m3_13)$r.squared[1],
                 summary(p4m4_13)$r.squared[1]),

  # 观测数量
  Obs_13class = c(nobs(p3way1_13), nobs(p3way2_13), nobs(p3way3_13),
                  nobs(p4m1_13), nobs(p4m2_13), nobs(p4m3_13), nobs(p4m4_13))
)

# 格式化数值
model_comparison$R2_11class <- round(model_comparison$R2_11class, 4)
model_comparison$R2_13class <- round(model_comparison$R2_13class, 4)

# 显示比较表
kable(model_comparison,
      col.names = c("模型", "R² (11类)", "观测数 (11类)", "R² (13类)", "观测数 (13类)"),
      caption = "11类与13类行业分类模型拟合度比较") %>%
  kable_styling(bootstrap_options = c("striped", "hover", "condensed"),
                full_width = FALSE)

# 计算改进程度
cat("\n=== 模型改进分析 ===\n")
cat("平均R²改进 (13类 vs 11类):",
    round(mean(model_comparison$R2_13class - model_comparison$R2_11class), 4), "\n")
cat("平均观测数增加 (13类 vs 11类):",
    round(mean(model_comparison$Obs_13class - model_comparison$Obs_11class), 0), "\n")
```

## 结论

基于11类和13类行业分类的回归分析结果显示：

1.  **数据覆盖度**: 13类分类实现了100%的数据覆盖，而11类分类有约47%的观测值无法分类
2.  **模型拟合度**: 13类分类模型在R²和AIC指标上的表现
3.  **政策含义**: 更细致的行业分类有助于更准确地控制行业固定效应

```{r save_results}
# 保存主要结果到HTML文件
stargazer(p3way3_11, p4m2_11, p4m4_11, p3way3_13, p4m2_13, p4m4_13,
          type="html",
          out="industry_classification_results.html",
          column.labels = c("P3-11类", "P4中央-11类", "P4地方-11类",
                           "P3-13类", "P4中央-13类", "P4地方-13类"),
          title="行业分类回归结果比较",
          omit="as.factor",
          notes=c("*** p<0.01, ** p<0.05, * p<0.1",
                  "省份和行业固定效应已包含"),
          dep.var.labels = "环境信息披露")

cat("结果已保存到 industry_classification_results.html\n")
```