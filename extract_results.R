# 提取模型比较结果的脚本
library(plm)
library(lme4)
library(lmerTest)
library(dplyr)
library(performance)
library(MuMIn)

# 加载数据
load("dta1_20240903.RData")

# 创建11类行业分类
dta1$industry_type11 <- case_when(
  # Energy
  dta1$IndustryName %in% c("石油和天然气开采业",
                           "石油加工、炼焦及核燃料加工业",
                           "煤炭开采和洗选业") ~ "Energy",
  # Materials
  dta1$IndustryName %in% c("化学原料及化学制品制造业","化学纤维制造业",
                           "黑色金属冶炼及压延加工业","有色金属冶炼及压延加工业",
                           "黑色金属矿采选业","有色金属矿采选业","非金属矿采选业",
                           "非金属矿物制品业","橡胶和塑料制品业","造纸及纸制品业",
                           "木材加工及木、竹、藤、棕、草制品业", "开采辅助活动", "林业",
                           "金属制品业") ~ "Materials",
  # Industrials
  dta1$IndustryName %in% c("铁路、船舶、航空航天和其它运输设备制造业","专用设备制造业",
                           "电气机械及器材制造业","通用设备制造业",
                           "装卸搬运和运输代理业","道路运输业","水上运输业",
                           "航空运输业","铁路运输业","仓储业","其他制造业",
                           "专业技术服务业","其他服务业",
                           "废弃资源综合利用业", "综合", "金属制品、机械和设备修理业",
                           "邮政业","印刷和记录媒介复制业",
                           "房屋建筑业","建筑安装业","土木工程建筑业",
                           "建筑装饰和其他建筑业","生态保护和环境治理业",
                           "公共设施管理业") ~ "Industrials",
  # Consumer Discretionary
  dta1$IndustryName %in% c("住宿业","餐饮业","体育","居民服务业","批发业",
                           "教育","纺织业","零售业","纺织服装、服饰业",
                           "文教、工美、体育和娱乐用品制造业","文化艺术业",
                           "皮革、毛皮、羽毛及其制品和制鞋业","家具制造业",
                           "机动车、电子产品和日用产品修理业",
                           "汽车制造业") ~ "Consumer Discretionary",
  # Consumer Staples
  dta1$IndustryName %in% c("酒、饮料和精制茶制造业","食品制造业","农副食品加工业",
                           "农业","畜牧业","渔业",
                           "农、林、牧、渔服务业") ~ "Consumer Staples",
  # Health Care
  dta1$IndustryName %in% c("医药制造业","卫生") ~ "Health Care",
  # Financials
  dta1$IndustryName %in% c("货币金融服务","资本市场服务","保险业",
                           "其他金融业","租赁业","商务服务业") ~ "Financials",
  # Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业",
                           "计算机、通信和其他电子设备制造业",
                           "仪器仪表制造业",
                           "研究和试验发展","科技推广和应用服务业") ~ "Information Technology",
  # Communication Services
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务",
                           "广播、电视、电影和影视录音制作业",
                           "新闻和出版业","互联网和相关服务") ~ "Communication Services",
  # Utilities
  dta1$IndustryName %in% c("电力、热力生产和供应业",
                           "燃气生产和供应业",
                           "水的生产和供应业") ~ "Utilities",
  # Real Estate
  dta1$IndustryName %in% c("房地产业","开发辅助活动") ~ "Real Estate",
  TRUE ~ NA_character_
)

# 创建去除NA的数据集
dta1_11class <- dta1 %>% filter(!is.na(industry_type11))

cat("=== 数据概况 ===\n")
cat("总观测数:", nrow(dta1), "\n")
cat("去除NA后观测数:", nrow(dta1_11class), "\n")
cat("公司数:", length(unique(dta1_11class$Symbol)), "\n")
cat("行业分类数:", length(unique(dta1_11class$industry_type11)), "\n\n")

# 创建一个简化的模型比较（只用P3_1作为示例）
cat("=== 运行示例模型比较 ===\n")

# 基准模型
baseline <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY), 
                data = dta1_11class)

# 固定效应模型
fixed_model <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log + as.factor(EndYear) + as.factor(industry_type11) + (1 | PROVINCE/CITY),
                   data = dta1_11class)

# 随机效应模型
random_model <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                    RegisterCapital_log + as.factor(EndYear) + (1 | PROVINCE/CITY) + (1 | industry_type11), 
                    data = dta1_11class)

# 比较AIC
cat("基准模型 AIC:", AIC(baseline), "\n")
cat("固定效应模型 AIC:", AIC(fixed_model), "\n")
cat("随机效应模型 AIC:", AIC(random_model), "\n\n")

# AIC变化
cat("固定效应 AIC变化:", AIC(fixed_model) - AIC(baseline), "\n")
cat("随机效应 AIC变化:", AIC(random_model) - AIC(baseline), "\n\n")

# 似然比检验
cat("=== 似然比检验 ===\n")
lr_fixed <- anova(baseline, fixed_model)
lr_random <- anova(baseline, random_model)

cat("基准 vs 固定效应 p值:", lr_fixed$`Pr(>Chisq)`[2], "\n")
cat("基准 vs 随机效应 p值:", lr_random$`Pr(>Chisq)`[2], "\n\n")

# R²比较
r2_baseline <- r.squaredGLMM(baseline)
r2_fixed <- r.squaredGLMM(fixed_model)
r2_random <- r.squaredGLMM(random_model)

cat("=== R²比较 ===\n")
cat("基准模型 边际R²:", r2_baseline[1], "条件R²:", r2_baseline[2], "\n")
cat("固定效应模型 边际R²:", r2_fixed[1], "条件R²:", r2_fixed[2], "\n")
cat("随机效应模型 边际R²:", r2_random[1], "条件R²:", r2_random[2], "\n\n")

# 结论
cat("=== 示例结论 ===\n")
if(AIC(fixed_model) > AIC(baseline)) {
  cat("✓ 固定效应模型AIC变差\n")
} else {
  cat("✗ 固定效应模型AIC改善\n")
}

if(AIC(random_model) > AIC(baseline)) {
  cat("✓ 随机效应模型AIC变差\n")
} else {
  cat("✗ 随机效应模型AIC改善\n")
}

if(lr_fixed$`Pr(>Chisq)`[2] > 0.05) {
  cat("✓ 固定效应没有显著改善\n")
} else {
  cat("✗ 固定效应显著改善\n")
}

if(lr_random$`Pr(>Chisq)`[2] > 0.05) {
  cat("✓ 随机效应没有显著改善\n")
} else {
  cat("✗ 随机效应显著改善\n")
}
